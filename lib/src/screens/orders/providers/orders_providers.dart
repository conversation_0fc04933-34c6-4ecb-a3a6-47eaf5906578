import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/orders_controller.dart';
import '../models/orders_response.model.dart';
import '../models/station.model.dart';
import '../repositories/orders_repository.dart';
import '../models/pricing.model.dart';

// * Orders Repo Provider ========================================
final ordersRepoProvider = Provider<OrdersRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return OrdersRepository(networkApiService: networkApiService);
});

// * Orders Controller Provider ========================================
final ordersControllerProvider = Provider<OrdersController>(
  (ref) {
    final ordersRepo = ref.watch(ordersRepoProvider);

    return OrdersController(
      ordersRepo: ordersRepo,
    );
  },
);

// * Get Stations Future Provider ========================================
final getStationsFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getStations();
  },
);

// * Get Stations with Location Future Provider ========================================
final getStationsWithLocationFutureProvider =
    FutureProvider.family<List<Station>, Map<String, double>?>(
  (ref, locationParams) {
    final ordersController = ref.read(ordersControllerProvider);

    return ordersController.getStationsWithLocation(
      lat: locationParams?['lat'],
      lng: locationParams?['lng'],
    );
  },
);

// * Get Types Future Provider ========================================
final getTypesFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getTypes();
  },
);

// * Get Pricing Future Provider ========================================
final getPricingFutureProvider =
    FutureProvider.family<Pricing, Map<String, int>>(
  (ref, params) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getPricing(
      fromStationId: params['fromStationId']!,
      toStationId: params['toStationId']!,
      typeId: params['typeId']!,
    );
  },
);

// * Get Orders Future Provider ========================================
final getOrdersFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrders();
  },
);

// * Current Status State Providers ========================================
final currentSentStatusProvider = StateProvider<String>((ref) => 'pending');
final currentReceivedStatusProvider =
    StateProvider<String>((ref) => 'waiting_for_order');

// * Get Orders History Future Provider ========================================
final getOrdersHistoryFutureProvider =
    FutureProvider.family<OrdersResponseModel, String>(
  (ref, status) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrdersHistory(status: status);
  },
);

// * Get Sent Orders History Provider ========================================
final getSentOrdersHistoryProvider = FutureProvider(
  (ref) {
    final status = ref.watch(currentSentStatusProvider);
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrdersHistory(status: status);
  },
);

// * Get Received Orders History Provider ========================================
final getReceivedOrdersHistoryProvider = FutureProvider(
  (ref) {
    final status = ref.watch(currentReceivedStatusProvider);
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrdersHistory(status: status);
  },
);

// * Countdown State Providers ========================================
// Global state to track countdown for each order
final orderCountdownProvider = StateProvider.family<int?, int>((ref, orderId) {
  return null; // Will be initialized when first accessed
});

// Provider to track start times for countdown calculation
final orderStartTimeProvider =
    StateProvider.family<DateTime?, int>((ref, orderId) {
  return null;
});
