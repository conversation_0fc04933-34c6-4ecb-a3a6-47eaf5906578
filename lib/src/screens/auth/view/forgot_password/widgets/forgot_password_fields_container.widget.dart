import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:dropx/src/screens/auth/providers/auth_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class ForgotPasswordFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const ForgotPasswordFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final isFormValid = useState(false);
    final phoneValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = phoneValid.value;
      return null;
    }, [phoneValid.value]);

    void sendCode() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      authController.forgotPassword(phone: data[FieldsConsts.mobile]);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: context.height * 0.12),

            // Message
            Text(
              context.tr.forgotPasswordMessage,
              textAlign: TextAlign.center,
              style: AppTextStyles.headLine.copyWith(
                fontSize: 22,
                color: Colors.black87,
              ),
            ),

            SizedBox(height: context.height * 0.1),

            // Phone Number Field
            BaseTextField(
              name: FieldsConsts.mobile,
              initialValue: kDebugMode ? '0506487448' : '',
              title: context.tr.mobileNumber,
              hint: context.tr.phoneHint,
              textInputType: TextInputType.phone,
              useUnderlineBorder: true,
              validator: (value) => Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.mobileNumber,
                invalidMessage: context.tr.invalidPhoneNumber,
              ),
              realTimeValidator: (value) {
                final error = Validations.palestinianPhoneNumber(
                  value,
                  emptyMessage: context.tr.mobileNumber,
                  invalidMessage: context.tr.invalidPhoneNumber,
                );
                phoneValid.value = error == null;
                return error;
              },
            ),

            SizedBox(height: context.height * 0.15),

            // Send Code Button
            SizedBox(
              height: 50,
              width: double.infinity,
              child: Button(
                onPressed: isFormValid.value && !authController.isLoading
                    ? sendCode
                    : null,
                label: context.tr.sendCode,
                isLoading: authController.isLoading,
                loadingWidget: LoadingWidget(),
              ),
            ),

            AppGaps.gap48,
          ],
        ),
      ),
    );
  }
}
