import 'package:dropx/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart';
import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:dropx/src/core/shared/widgets/search_bar_widget/search_bar.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductsScreen extends StatelessWidget {
  const ProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: context.tr.stores,
      ),
      body: Padding(
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
        child: Column(
          children: [
            AppGaps.gap12,

            // * Search Bar
            SearchBarWidget(
              label: context.tr.searchForProducts,
            ),

            AppGaps.gap12,

            // * Products Grid
            const Expanded(child: ProductsGridWidget()),
          ],
        ),
      ),
    );
  }
}
