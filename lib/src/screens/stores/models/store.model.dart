class StoreModel {
  final String title;
  final String image;
  final String suitableFor;
  final int sessionNo;
  final int seatsNo;
  final String period;
  final String payment;
  final String status;
  final int sessionsPerMonth;
  final int monthPrice;
  final int coursePrice;
  final String? deadLine;
  final String firstDay;
  final String secondDay;
  final String? thirdDay;
  final String firstFromTime;
  final String firstToTime;
  final String secondFromTime;
  final String secondToTime;
  final String? thirdFromTime;
  final String? thirdToTime;
  final String? notes;
  final int coursePriceBefore;
  final String? discountTo;
  final bool isDiscount;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isReserved;
  final num price;
  final num discount;
  final String description;
  final String? startDate;

  DateTime get startDateTime {
    final startDate = this.startDate ?? '';
    final startTime = firstFromTime;

    if (startDate.isNotEmpty && startTime.isNotEmpty) {
      final date = DateTime.parse(startDate);
      final timeParts = startTime.split(':');
      final hours = int.parse(timeParts[0]);
      final minutes = int.parse(timeParts[1]);
      return DateTime(date.year, date.month, date.day, hours, minutes);
    }

    return DateTime.now();
  }

  StoreModel({
    this.title = '',
    this.description = '',
    this.price = 0,
    this.discount = 0,
    this.startDate,
    this.suitableFor = '',
    this.image = '',
    this.sessionNo = 0,
    this.seatsNo = 0,
    this.period = '',
    this.payment = '',
    this.status = '',
    this.sessionsPerMonth = 0,
    this.monthPrice = 0,
    this.coursePrice = 0,
    this.deadLine,
    this.firstDay = '',
    this.secondDay = '',
    this.thirdDay,
    this.firstFromTime = '',
    this.firstToTime = '',
    this.secondFromTime = '',
    this.secondToTime = '',
    this.thirdFromTime,
    this.thirdToTime,
    this.notes,
    this.coursePriceBefore = 0,
    this.discountTo,
    this.isDiscount = false,
    this.createdAt,
    this.updatedAt,
    this.isReserved = false,
  });

  factory StoreModel.fromJson(Map<String, dynamic> json) {
    return StoreModel(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      startDate: json['date_start'],
      price: json['course_price'] ?? 0,
      discount: json['course_price_before'] ?? 0,
      suitableFor: json['suitable_for'] ?? '',
      image: json['feature']['image'] ?? '',
      sessionNo: json['session_no'] ?? 0,
      seatsNo: json['seats_no'] ?? 0,
      period: json['period'] ?? '',
      payment: json['payment'] ?? '',
      status: json['status'] ?? '',
      sessionsPerMonth: json['sessions_per_month'] ?? 0,
      monthPrice: json['month_price'] ?? 0,
      coursePrice: json['course_price'] ?? 0,
      deadLine: json['dead_line'],
      firstDay: json['first_day'] ?? '',
      secondDay: json['second_day'] ?? '',
      thirdDay: json['third_day'],
      firstFromTime: json['first_from_time'] ?? '',
      firstToTime: json['first_to_time'] ?? '',
      secondFromTime: json['second_from_time'] ?? '',
      secondToTime: json['second_to_time'] ?? '',
      thirdFromTime: json['third_from_time'],
      thirdToTime: json['third_to_time'],
      notes: json['notes'],
      coursePriceBefore: json['course_price_before'] ?? 0,
      discountTo: json['discount_to'],
      isDiscount: json['is_discount'] == '1',
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      isReserved: json['is_reserved'] ?? false,
    );
  }

  // demo stores with only title and image and description
  static final stores = [
    StoreModel(
      title: 'Pet Store',
      image:
          'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
      description: 'Pet Store Description',
    ),
    StoreModel(
      title: 'Pet Store',
      image:
          'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
      description: 'Pet Store Description',
    ),
    StoreModel(
      title: 'Pet Store',
      image:
          'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
      description: 'Pet Store Description',
    ),
    StoreModel(
      title: 'Pet Store',
      image:
          'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
      description: 'Pet Store Description',
    ),
  ];
}
