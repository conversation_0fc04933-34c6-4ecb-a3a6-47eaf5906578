class SearchRequest {
  final int fromStationId;
  final int toStationId;

  SearchRequest({
    required this.fromStationId,
    required this.toStationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'from_station_id': fromStationId,
      'to_station_id': toStationId,
    };
  }

  Map<String, String> toQueryParams() {
    return {
      'from_station_id': fromStationId.toString(),
      'to_station_id': toStationId.toString(),
    };
  }
}

class AttendanceRequest {
  final int fromStationId;
  final int toStationId;
  final int typeId;

  AttendanceRequest({
    required this.fromStationId,
    required this.toStationId,
    required this.typeId,
  });

  Map<String, dynamic> toJson() {
    return {
      'from_station_id': fromStationId,
      'to_station_id': toStationId,
      'type_id': typeId,
    };
  }
}

class InterestRequest {
  final int fromStationId;
  final int toStationId;
  final int typeId;

  InterestRequest({
    required this.fromStationId,
    required this.toStationId,
    required this.typeId,
  });

  Map<String, dynamic> toJson() {
    return {
      'from_station_id': fromStationId,
      'to_station_id': toStationId,
      'type_id': typeId,
    };
  }
}
