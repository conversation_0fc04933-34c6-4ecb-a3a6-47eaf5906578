import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/core/shared/models/page_content_model.dart';
import 'package:xr_helper/xr_helper.dart';

class PageContentRepository with BaseRepository {
  final BaseApiServices networkApiService;

  PageContentRepository({
    required this.networkApiService,
  });

  // * Get Privacy Policy
  Future<PageContentModel> getPrivacyPolicy() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.privacyPolicy;

        final response = await networkApiService.getResponse(url);

        return PageContentModel.fromJson(response['data']);
      },
    );
  }

  // * Get About Us
  Future<PageContentModel> getAboutUs() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.aboutUs;

        final response = await networkApiService.getResponse(url);

        return PageContentModel.fromJson(response['data']);
      },
    );
  }

  // * Get Terms of Service
  Future<PageContentModel> getTermsOfService() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.termsOfService;

        final response = await networkApiService.getResponse(url);

        return PageContentModel.fromJson(response['data']);
      },
    );
  }
}
