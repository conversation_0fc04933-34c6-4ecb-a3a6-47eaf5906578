import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/screens/auth/view/register/widgets/terms_checkbox.widget.dart';
import 'package:dropx/src/screens/auth/view/register/widgets/register_buttons.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../providers/auth_providers.dart';

class RegisterFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const RegisterFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final termsAccepted = useState(false);
    final isFormValid = useState(false);

    // Form validation state
    final nameValid = useState(false);
    final phoneValid = useState(false);
    final passwordValid = useState(false);
    final confirmPasswordValid = useState(false);
    final idValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = nameValid.value &&
          phoneValid.value &&
          passwordValid.value &&
          confirmPasswordValid.value &&
          idValid.value &&
          termsAccepted.value;
      return null;
    }, [
      nameValid.value,
      phoneValid.value,
      passwordValid.value,
      confirmPasswordValid.value,
      idValid.value,
      termsAccepted.value,
    ]);

    void register() {
      if (!formKey.currentState!.saveAndValidate()) return;

      if (!termsAccepted.value) {
        showToast(context.tr.pleaseAcceptTerms, isError: true);
        return;
      }

      final data = formKey.currentState?.instantValue ?? {};

      authController.register(data: data);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppGaps.gap16,

          // Name Field
          BaseTextField(
            name: FieldsConsts.name,
            title: context.tr.fullName,
            hint: context.tr.fullName,
            useUnderlineBorder: true,
            validator: (value) => Validations.mustBeNotEmpty(
              value,
              emptyMessage: context.tr.fullName,
            ),
            realTimeValidator: (value) {
              final isValid = Validations.mustBeNotEmpty(value) == null;
              nameValid.value = isValid;
              return null; // Don't show error in real-time for name
            },
          ),

          AppGaps.gap16,

          // Phone Number Field
          BaseTextField(
            name: FieldsConsts.mobile,
            title: context.tr.mobileNumber,
            hint: context.tr.phoneHint,
            textInputType: TextInputType.phone,
            useUnderlineBorder: true,
            validator: (value) => Validations.palestinianPhoneNumber(
              value,
              emptyMessage: context.tr.mobileNumber,
              invalidMessage: context.tr.invalidPhoneNumber,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.mobileNumber,
                invalidMessage: context.tr.invalidPhoneNumber,
              );
              phoneValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Password Field
          BaseTextField(
            name: FieldsConsts.password,
            title: context.tr.password,
            hint: context.tr.password,
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            useUnderlineBorder: true,
            validator: (value) => Validations.password(
              value,
              emptyPasswordMessage: context.tr.password,
            ),
            realTimeValidator: (value) {
              final error = Validations.password(
                value,
                emptyPasswordMessage: context.tr.password,
              );
              passwordValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Confirm Password Field
          BaseTextField(
            name: FieldsConsts.confirmPassword,
            title: context.tr.confirmPassword,
            hint: context.tr.confirmPassword,
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            useUnderlineBorder: true,
            validator: (value) {
              final password =
                  formKey.currentState?.fields[FieldsConsts.password]?.value;
              return Validations.confirmPassword(
                value,
                password,
                emptyMessage: context.tr.confirmPassword,
                mismatchMessage: context.tr.passwordsDoNotMatch,
              );
            },
            realTimeValidator: (value) {
              final password =
                  formKey.currentState?.fields[FieldsConsts.password]?.value;
              final error = Validations.confirmPassword(
                value,
                password,
                emptyMessage: context.tr.confirmPassword,
                mismatchMessage: context.tr.passwordsDoNotMatch,
              );
              confirmPasswordValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // ID Number Field
          BaseTextField(
            name: FieldsConsts.idNumber,
            title: context.tr.idNumber,
            hint: context.tr.idNumber,
            textInputType: TextInputType.number,
            initialValue: kDebugMode ? '329491948' : null,
            useUnderlineBorder: true,
            validator: (value) => Validations.palestinianId(
              value,
              emptyMessage: context.tr.idNumber,
              invalidMessage: context.tr.invalidIdNumber,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianId(
                value,
                emptyMessage: context.tr.idNumber,
                invalidMessage: context.tr.invalidIdNumber,
              );
              idValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Terms and Conditions
          TermsCheckbox(termsAccepted: termsAccepted),

          AppGaps.gap24,

          // Buttons
          RegisterButtons(
            isLoading: authController.isLoading,
            isFormValid: isFormValid.value,
            onRegister: register,
          ),

          AppGaps.gap16,
        ],
      ),
    );
  }
}
