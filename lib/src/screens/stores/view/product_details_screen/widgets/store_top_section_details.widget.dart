import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/theme/color_manager.dart';

class StoreTopSectionDetailsWidget extends StatelessWidget {
  const StoreTopSectionDetailsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(AppRadius.radius20),
            bottomRight: Radius.circular(AppRadius.radius20),
          ),
          child: BaseCachedImage(
            'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
            height: 300.h,
            width: context.width,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // fav
            const CircleAvatar(
              radius: 22,
              backgroundColor: ColorManager.white,
              child: Icon(
                Icons.favorite_border,
                color: ColorManager.primaryColor,
                size: 20,
              ),
            ),
            InkWell(
              onTap: () {
                context.back();
              },
              child: const CircleAvatar(
                radius: 22,
                backgroundColor: ColorManager.primaryColor,
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            )
          ],
        ).paddingSymmetric(
          vertical: AppSpaces.padding24,
          horizontal: AppSpaces.padding12,
        ),
      ],
    );
  }
}
