import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/shared/widgets/app_bar/base_header.widget.dart';
import 'widgets/new_password_fields_container.widget.dart';

class NewPasswordScreen extends HookConsumerWidget {
  final String phone;

  const NewPasswordScreen({
    super.key,
    required this.phone,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: FormBuilder(
          key: formKey,
          child: Column(
            children: [
              // Header
              BaseHeaderWidget(
                title: context.tr.newPasswordTitle,
                withBackButton: true,
              ),

              // Form Content
              Expanded(
                child: NewPasswordFieldsContainer(
                  formKey: formKey,
                  phone: phone,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
