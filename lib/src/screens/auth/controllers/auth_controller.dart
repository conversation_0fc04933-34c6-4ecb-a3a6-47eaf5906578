import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/screens/auth/models/user_model.dart';
import 'package:dropx/src/screens/auth/repositories/auth_repository.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:dropx/src/screens/auth/view/verification/verification.screen.dart';
import 'package:dropx/src/screens/auth/view/new_password/new_password.screen.dart';
import 'package:dropx/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setLoginUser(data);

        final userData = await authRepo.login(user: user);

        return userData;
      },
      additionalFunction: () {
        const MainScreen().navigateReplacement;
      },
    );
  }

  // * Register
  Future<Map<String, dynamic>> register({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setRegisterUser(data);

        final response = await authRepo.register(user: user);

        return response;
      },
      additionalFunction: () {
        VerificationScreen(
          phone: data[FieldsConsts.mobile],
          userData: data,
        ).navigate;
      },
    );
  }

  // * Verify Code
  Future<bool> verifyCode({
    required Map<String, dynamic> userData,
    required String code,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setRegisterUser(userData);

        final result = await authRepo.verifyCode(user: user, code: code);

        return result;
      },
      additionalFunction: () {
        const MainScreen().navigateReplacement;
      },
    );
  }

  Future<void> resendCode({
    required String phone,
  }) async {
    return await baseFunction(
      () async {
        await authRepo.resendCode(phone: phone);
      },
    );
  }

  // * Forgot Password
  Future<bool> forgotPassword({
    required String phone,
  }) async {
    return await baseFunction(
      () async {
        final result = await authRepo.forgotPassword(phone: phone);
        return result;
      },
      additionalFunction: () {
        VerificationScreen(
          phone: phone,
          userData: {},
          isFromForgotPassword: true,
        ).navigate;
      },
    );
  }

  // * Verify Reset Code
  Future<bool> verifyResetCode({
    required String phone,
    required String code,
    required bool isFromForgotPassword,
    required bool isFromProfileUpdate,
    required Map<String, dynamic> userData,
  }) async {
    return await baseFunction(
      () async {
        final result = await authRepo.verifyResetCode(
          phone: phone,
          code: code,
        );
        return result;
      },
      additionalFunction: () {
        if (isFromForgotPassword) {
          NewPasswordScreen(phone: phone).navigate;
        } else if (isFromProfileUpdate) {
          // Update user data with new phone and navigate back
          final updatedUserData = Map<String, dynamic>.from(userData);
          updatedUserData[FieldsConsts.mobile] = phone;
          authRepo.updateLocalUserData(updatedUserData);
          const MainScreen().navigateReplacement;
        }
      },
    );
  }

  // * Reset Password
  Future<bool> resetPassword({
    required String phone,
    required String password,
  }) async {
    return await baseFunction(
      () async {
        final result = await authRepo.resetPassword(
          phone: phone,
          password: password,
        );
        return result;
      },
      additionalFunction: () {
        const MainScreen().navigateReplacement;
      },
    );
  }

  // * Update Profile
  Future<bool> updateProfile({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final result = await authRepo.updateProfile(data: data);
        return result;
      },
      additionalFunction: () {
        // Check if phone was changed
        final currentUser = UserModel.currentUser();
        final newPhone = data[FieldsConsts.mobile];

        if (newPhone != null && newPhone != currentUser.phone) {
          // Navigate to verification for new phone
          VerificationScreen(
            phone: newPhone,
            userData: data,
            isFromProfileUpdate: true,
          ).navigate;
        } else {
          // Update local data and show success
          authRepo.updateLocalUserData(data);
          showToast('تم تحديث الملف الشخصي بنجاح');
        }
      },
    );
  }

  // * Set Login User
  Future<UserModel> _setLoginUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
    );

    return user;
  }

  // * Set Register User
  Future<UserModel> _setRegisterUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      name: data[FieldsConsts.name],
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
      nationalId: data[FieldsConsts.idNumber],
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await authRepo.logout();

        const LoginScreen().navigateReplacement;
      },
    );
  }

// * Get Countries
// Future<List<CountryModel>> getCountries() async {
//   return await baseFunction(
//     () async {
//       return await authRepo.getCountries();
//     },
//   );
// }
}
