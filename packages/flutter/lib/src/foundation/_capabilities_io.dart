// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// The dart:io implementation of [isCanvasKit].
///
/// This bool shouldn't be used outside of web.
bool get isCanvasKit {
  throw UnimplementedError('isCanvasKit is not implemented for dart:io.');
}

/// The dart:io implementation of [isSkwasm].
///
/// This bool shouldn't be used outside of web.
bool get isSkwasm {
  throw UnimplementedError('isSkwasm is not implemented for dart:io.');
}
