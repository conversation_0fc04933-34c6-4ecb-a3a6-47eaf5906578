import 'package:equatable/equatable.dart';

class Pricing extends Equatable {
  final int id;
  final String fromStation;
  final String toStation;
  final String type;
  final String price;

  const Pricing({
    required this.id,
    this.fromStation = '',
    this.toStation = '',
    this.type = '',
    this.price = '',
  });

  factory Pricing.fromJson(Map<String, dynamic> json) {
    return Pricing(
      id: json['id'],
      fromStation: json['from_station'] ?? '',
      toStation: json['to_station'] ?? '',
      type: json['type'] ?? '',
      price: json['price'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from_station': fromStation,
      'to_station': toStation,
      'type': type,
      'price': price,
    };
  }

  @override
  List<Object?> get props => [
        id,
        fromStation,
        toStation,
        type,
        price,
      ];
}
