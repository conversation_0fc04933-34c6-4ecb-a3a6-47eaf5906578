import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:dropx/generated/assets.gen.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:xr_helper/xr_helper.dart';

class OnBoardingScreen extends HookWidget {
  const OnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();

    final currentIndex = useState(0);

    final images = [
      Assets.images.onboarding1,
      Assets.images.onboarding2,
      Assets.images.onboarding3,
    ];

    final title = [
      context.tr.onBoardingTitle1,
      context.tr.onBoardingTitle2,
      context.tr.onBoardingTitle3,
    ];

    final description = [
      context.tr.onBoardingDescription1,
      context.tr.onBoardingDescription2,
      context.tr.onBoardingDescription3,
    ];

    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          PageView.builder(
              onPageChanged: (index) {
                currentIndex.value = index;
              },
              itemCount: images.length,
              controller: pageController,
              itemBuilder: (context, index) => images[index].image(
                    width: double.infinity,
                    fit: BoxFit.cover,
                  )),
          Container(
            height: context.height * .45,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSpaces.padding24, vertical: AppSpaces.padding48),
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.radius20),
                topRight: Radius.circular(AppRadius.radius20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Center(
                        child: SmoothPageIndicator(
                          controller: pageController,
                          count: images.length,
                          axisDirection: Axis.horizontal,
                          effect: const ExpandingDotsEffect(
                            dotHeight: 12,
                            dotWidth: 12,
                            activeDotColor: ColorManager.primaryColor,
                            dotColor: Color(0xffdbdbdb),
                          ),
                        ),
                      ),
                      AppGaps.gap24,
                      Text(title[currentIndex.value],
                          style: AppTextStyles.headLine),
                      AppGaps.gap24,
                      Text(description[currentIndex.value],
                          style: AppTextStyles.subTitle.copyWith(fontSize: 22)),
                    ],
                  ),
                ),
                Button(
                  label: currentIndex.value == 2
                      ? context.tr.startNow
                      : context.tr.next,
                  onPressed: () {
                    if (currentIndex.value == 2) {
                      const LoginScreen().navigateReplacement;
                    }

                    pageController.nextPage(
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.fastOutSlowIn);
                  },
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
