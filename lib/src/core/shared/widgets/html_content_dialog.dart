import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/providers/page_content_providers.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

enum PageType { privacyPolicy, aboutUs, termsOfService }

class HtmlContentDialog extends ConsumerStatefulWidget {
  final PageType pageType;

  const HtmlContentDialog({
    super.key,
    required this.pageType,
  });

  static void show(BuildContext context, PageType pageType) {
    showDialog(
      context: context,
      builder: (context) => HtmlContentDialog(pageType: pageType),
    );
  }

  @override
  ConsumerState<HtmlContentDialog> createState() => _HtmlContentDialogState();
}

class _HtmlContentDialogState extends ConsumerState<HtmlContentDialog> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadContent();
    });
  }

  void _loadContent() {
    final controller = ref.read(pageContentControllerProvider.notifier);

    switch (widget.pageType) {
      case PageType.privacyPolicy:
        controller.getPrivacyPolicy();
        break;
      case PageType.aboutUs:
        controller.getAboutUs();
        break;
      case PageType.termsOfService:
        controller.getTermsOfService();
        break;
    }
  }

  String _getTitle() {
    switch (widget.pageType) {
      case PageType.privacyPolicy:
        return context.tr.privacyPolicy;
      case PageType.aboutUs:
        return context.tr.aboutUs;
      case PageType.termsOfService:
        return context.tr.termsDialogTitle;
    }
  }

  @override
  Widget build(BuildContext context) {
    final pageContentState = ref.watch(pageContentControllerProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _getTitle(),
                      style: AppTextStyles.title.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    color: ColorManager.primaryColor,
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: pageContentState.when(
                data: (pageContent) {
                  if (pageContent == null) {
                    return const SizedBox.shrink();
                  }

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Html(
                      data: pageContent.content,
                      style: {
                        "body": Style(
                          fontSize: FontSize(14),
                          lineHeight: const LineHeight(1.5),
                          color: Colors.black87,
                        ),
                        "h1": Style(
                          fontSize: FontSize(20),
                          fontWeight: FontWeight.bold,
                          color: ColorManager.primaryColor,
                          margin: Margins.only(bottom: 16),
                        ),
                        "h2": Style(
                          fontSize: FontSize(18),
                          fontWeight: FontWeight.bold,
                          color: ColorManager.primaryColor,
                          margin: Margins.only(bottom: 12),
                        ),
                        "h3": Style(
                          fontSize: FontSize(16),
                          fontWeight: FontWeight.bold,
                          color: ColorManager.primaryColor,
                          margin: Margins.only(bottom: 8),
                        ),
                        "p": Style(
                          margin: Margins.only(bottom: 12),
                        ),
                        "ul": Style(
                          margin: Margins.only(bottom: 12),
                        ),
                        "li": Style(
                          margin: Margins.only(bottom: 4),
                        ),
                      },
                    ),
                  );
                },
                loading: () => const Padding(
                  padding: EdgeInsets.all(40),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stack) => Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red[400],
                      ),
                      AppGaps.gap16,
                      Text(
                        context.tr.errorOccurred,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Colors.red[400],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      AppGaps.gap16,
                      ElevatedButton(
                        onPressed: _loadContent,
                        child: Text(context.tr.retry),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    context.tr.close,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Clear the state when dialog is disposed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pageContentControllerProvider.notifier).clear();
    });
    super.dispose();
  }
}
