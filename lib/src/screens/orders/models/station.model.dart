import 'package:equatable/equatable.dart';

class Station extends Equatable {
  final int id;
  final String name;
  final String latitude;
  final String longitude;

  const Station({
    required this.id,
    this.name = '',
    this.latitude = '',
    this.longitude = '',
  });

  factory Station.fromJson(Map<String, dynamic> json) {
    return Station(
      id: json['id'],
      name: json['name'] ?? '',
      latitude: json['latitude'] ?? '',
      longitude: json['longitude'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        latitude,
        longitude,
      ];
}
