import 'package:equatable/equatable.dart';

class TrackingModel extends Equatable {
  final int id;
  final String status;
  final String statusText;
  final String note;
  final String user;
  final String station;

  const TrackingModel({
    required this.id,
    this.status = '',
    this.statusText = '',
    this.note = '',
    this.user = '',
    this.station = '',
  });

  factory TrackingModel.fromJson(Map<String, dynamic> json) {
    return TrackingModel(
      id: json['id'],
      status: json['status'] ?? '',
      statusText: json['status_text'] ?? '',
      note: json['note'] ?? '',
      user: json['user'] ?? '',
      station: json['station'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'status_text': statusText,
      'note': note,
      'user': user,
      'station': station,
    };
  }

  @override
  List<Object?> get props => [
        id,
        status,
        statusText,
        note,
        user,
        station,
      ];
}
