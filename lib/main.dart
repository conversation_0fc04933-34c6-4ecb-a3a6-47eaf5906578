import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropx/src/app.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const ProviderScope(child: BaseApp()));
}

 // Pricing should outside dialog too in create order page and show it whenever choose from and to and type
 // Location distance should be under button beside from station drop down
 // Update cards after each 5 seconds
 // Confirm Dialog before i am here button
 // First Tab
 // Check if pending order and click on delivered check location of to station
 // if first pending tab تسليم لمحطة الانطلاق check his current location with from station
 // change status to confirmed
 // Second Tab
 // If second confirmed tab استلام من محطة الانطلاق check his current location with to station
 // change status to picked_up
 // Third button تسليم لنقطة الانطلاق and check his current location with to station
 // change status to delivered
