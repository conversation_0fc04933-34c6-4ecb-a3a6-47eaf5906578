# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for ListWheelScrollView from the Widgets library. *
#   For fixes to
#     * Actions: fix_actions.yaml
#     * BuildContext: fix_build_context.yaml
#     * Element: fix_element.yaml
#     * Widgets (general): fix_widgets.yaml
version: 1
transforms:
  # Changes made in https://docs.flutter.dev/release/breaking-changes/clip-behavior
  - title: "Migrate to 'clipBehavior'"
    date: 2020-08-20
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      constructor: 'useDelegate'
      inClass: 'ListWheelScrollView'
    oneOf:
    - if: "clipToSize == 'true'"
      changes:
        - kind: 'addParameter'
          index: 13
          name: 'clipBehavior'
          style: optional_named
          argumentValue:
            expression: 'Clip.hardEdge'
            requiredIf: "clipToSize == 'true'"
        - kind: 'removeParameter'
          name: 'clipToSize'
    - if: "clipToSize == 'false'"
      changes:
        - kind: 'addParameter'
          index: 13
          name: 'clipBehavior'
          style: optional_named
          argumentValue:
            expression: 'Clip.none'
            requiredIf: "clipToSize == 'false'"
        - kind: 'removeParameter'
          name: 'clipToSize'
    variables:
      clipToSize:
        kind: 'fragment'
        value: 'arguments[clipToSize]'

  # Changes made in https://docs.flutter.dev/release/breaking-changes/clip-behavior
  - title: "Migrate to 'clipBehavior'"
    date: 2020-08-20
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      field: 'clipToSize'
      inClass: 'ListWheelScrollView'
    changes:
      - kind: 'rename'
        newName: 'clipBehavior'

  # Changes made in https://docs.flutter.dev/release/breaking-changes/clip-behavior
  - title: "Migrate to 'clipBehavior'"
    date: 2020-08-20
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      constructor: ''
      inClass: 'ListWheelScrollView'
    oneOf:
    - if: "clipToSize == 'true'"
      changes:
        - kind: 'addParameter'
          index: 13
          name: 'clipBehavior'
          style: optional_named
          argumentValue:
            expression: 'Clip.hardEdge'
            requiredIf: "clipToSize == 'true'"
        - kind: 'removeParameter'
          name: 'clipToSize'
    - if: "clipToSize == 'false'"
      changes:
        - kind: 'addParameter'
          index: 13
          name: 'clipBehavior'
          style: optional_named
          argumentValue:
            expression: 'Clip.none'
            requiredIf: "clipToSize == 'false'"
        - kind: 'removeParameter'
          name: 'clipToSize'
    variables:
      clipToSize:
        kind: 'fragment'
        value: 'arguments[clipToSize]'

# Before adding a new fix: read instructions at the top of this file.
