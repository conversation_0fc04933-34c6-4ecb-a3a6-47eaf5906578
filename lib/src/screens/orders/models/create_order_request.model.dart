import 'package:equatable/equatable.dart';

class CreateOrderRequest extends Equatable {
  final int fromStationId;
  final int toStationId;
  final int typeId;
  final String receiverName;
  final String receiverPhone;
  final String price;
  final String? note;
  final String paymentMethod;

  const CreateOrderRequest({
    required this.fromStationId,
    required this.toStationId,
    required this.typeId,
    required this.receiverName,
    required this.receiverPhone,
    required this.price,
    this.note,
    this.paymentMethod = 'cash',
  });

  Map<String, dynamic> toJson() {
    return {
      'from_station_id': fromStationId,
      'to_station_id': toStationId,
      'type_id': typeId,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'price': price,
      'note': note,
      'payment_method': paymentMethod,
    };
  }

  @override
  List<Object?> get props => [
        fromStationId,
        toStationId,
        typeId,
        receiverName,
        receiverPhone,
        price,
        note,
        paymentMethod,
      ];
}
