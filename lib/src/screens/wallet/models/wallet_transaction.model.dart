import 'package:equatable/equatable.dart';
import '../../orders/models/order.model.dart';

class WalletTransactionModel extends Equatable {
  final int id;
  final int points;
  final String type;
  final String description;
  final String createdAt;
  final OrderModel? order;

  const WalletTransactionModel({
    required this.id,
    required this.points,
    required this.type,
    required this.description,
    required this.createdAt,
    this.order,
  });

  factory WalletTransactionModel.fromJson(Map<String, dynamic> json) {
    return WalletTransactionModel(
      id: json['id'] ?? 0,
      points: json['points'] ?? 0,
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      createdAt: json['created_at'] ?? '',
      order: json['order'] != null
          ? OrderModel.fromJson(json['order'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'points': points,
      'type': type,
      'description': description,
      'created_at': createdAt,
      'order': order?.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        points,
        type,
        description,
        createdAt,
        order,
      ];
}

class WalletTransactionsPaginationLinks extends Equatable {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  const WalletTransactionsPaginationLinks({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory WalletTransactionsPaginationLinks.fromJson(Map<String, dynamic> json) {
    return WalletTransactionsPaginationLinks(
      first: json['first'],
      last: json['last'],
      prev: json['prev'],
      next: json['next'],
    );
  }

  @override
  List<Object?> get props => [first, last, prev, next];
}

class WalletTransactionsPaginationMeta extends Equatable {
  final int currentPage;
  final int? from;
  final int lastPage;
  final String path;
  final int perPage;
  final int? to;
  final int total;

  const WalletTransactionsPaginationMeta({
    required this.currentPage,
    this.from,
    required this.lastPage,
    required this.path,
    required this.perPage,
    this.to,
    required this.total,
  });

  factory WalletTransactionsPaginationMeta.fromJson(Map<String, dynamic> json) {
    return WalletTransactionsPaginationMeta(
      currentPage: json['current_page'] ?? 1,
      from: json['from'],
      lastPage: json['last_page'] ?? 1,
      path: json['path'] ?? '',
      perPage: json['per_page'] ?? 15,
      to: json['to'],
      total: json['total'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [currentPage, from, lastPage, path, perPage, to, total];
}

class WalletTransactionsResponse extends Equatable {
  final List<WalletTransactionModel> data;
  final WalletTransactionsPaginationLinks links;
  final WalletTransactionsPaginationMeta meta;

  const WalletTransactionsResponse({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory WalletTransactionsResponse.fromJson(Map<String, dynamic> json) {
    return WalletTransactionsResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => WalletTransactionModel.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      links: WalletTransactionsPaginationLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: WalletTransactionsPaginationMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }

  @override
  List<Object?> get props => [data, links, meta];
}
