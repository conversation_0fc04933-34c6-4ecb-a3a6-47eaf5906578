import 'package:dropx/src/screens/stores/models/store.model.dart';
import 'package:dropx/src/screens/stores/repositories/store.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class StoresController extends BaseVM {
  final StoresRepository storesRepo;

  StoresController({
    required this.storesRepo,
  });

  // * Get Stores
  Future<List<StoreModel>> getStores() async {
    return await baseFunction(
      () async {
        return await storesRepo.getStores();
      },
    );
  }

  // * Add Stores
  Future<void> addStores({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        return await storesRepo.addStores(data: data);
      },
    );
  }
}
