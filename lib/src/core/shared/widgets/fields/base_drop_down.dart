import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';

class BaseDropDown<T> extends StatelessWidget {
  final dynamic value;
  final String hint;
  final List<DropdownMenuItem<T>> data;
  final void Function(dynamic)? onChanged;
  final bool isExpanded;
  final EdgeInsets hintPadding;

  const BaseDropDown({
    super.key,
    required this.onChanged,
    this.hint = '',
    required this.data,
    required this.value,
    this.hintPadding = EdgeInsets.zero,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<T>(
      hint: Padding(
        padding: hintPadding,
        child: Text(
          hint,
          style: AppTextStyles.hint,
        ),
      ),
      borderRadius: BorderRadius.circular(
        AppRadius.radius12,
      ),
      iconEnabledColor: ColorManager.primaryColor,
      isExpanded: isExpanded,
      dropdownColor: ColorManager.white,
      value: value,
      icon: const Icon(
        Icons.keyboard_arrow_down,
        color: ColorManager.black,
      ),
      underline: const Center(),
      onChanged: onChanged,
      items: data,
    );
  }
}
