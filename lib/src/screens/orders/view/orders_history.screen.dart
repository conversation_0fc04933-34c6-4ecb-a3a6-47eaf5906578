import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:dropx/src/core/shared/widgets/tabs/custom_tab_bar.widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:dropx/src/screens/home/<USER>/shipment_card.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class OrdersHistoryScreen extends HookConsumerWidget {
  const OrdersHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mainTabIndex = useState(0);
    final sentTabIndex = useState(0);
    final receivedTabIndex = useState(0);
    final expandedCardId = useState<String?>(null);

    // Define status arrays for each tab
    final sentStatuses = ['pending', 'confirmed', 'picked_up', 'delivered'];
    final receivedStatuses = ['waiting_for_order', 'picked_up', 'delivered'];

    // Initialize the default statuses when the screen loads
    useEffect(() {
      // Set initial status for sent shipments (first tab)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(currentSentStatusProvider.notifier).state = sentStatuses[0];
        ref.read(currentReceivedStatusProvider.notifier).state =
            receivedStatuses[0];
      });
      return null;
    }, []);

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: true,
        title: Text(
          context.tr.ordersHistory,
          style: AppTextStyles.title,
        ),
      ),
      body: Column(
        children: [
          // Main Tab Bar (Sent/Received)
          Expanded(
            child: CustomTabBarWidget(
              initialIndex: mainTabIndex.value,
              onTabChanged: (index) {
                mainTabIndex.value = index;
                // Reset secondary tab indices when main tab changes
                sentTabIndex.value = 0;
                receivedTabIndex.value = 0;

                // Set the correct default status for each main tab
                if (index == 0) {
                  // Sent shipments - set to first status (pending)
                  ref.read(currentSentStatusProvider.notifier).state =
                      sentStatuses[0];
                } else {
                  // Received shipments - set to first status (waiting_for_order)
                  ref.read(currentReceivedStatusProvider.notifier).state =
                      receivedStatuses[0];
                }
              },
              tabTitles: [
                context.tr.sentShipments,
                context.tr.receivedShipments,
              ],
              children: [
                // Sent Shipments Tab with Status Filters
                _buildSentShipmentsTab(
                  context,
                  ref,
                  sentTabIndex,
                  expandedCardId,
                  sentStatuses,
                ),
                // Received Shipments Tab with Status Filters
                _buildReceivedShipmentsTab(
                  context,
                  ref,
                  receivedTabIndex,
                  expandedCardId,
                  receivedStatuses,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSentShipmentsTab(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> tabIndex,
    ValueNotifier<String?> expandedCardId,
    List<String> statuses,
  ) {
    return CustomTabBarWidget(
      key: ValueKey('sent_${tabIndex.value}'),
      // Force rebuild when tab index changes
      isScrollable: true,
      color: ColorManager.orangeColor,
      initialIndex: tabIndex.value,
      onTabChanged: (index) {
        tabIndex.value = index;
        // Update the current status to trigger refresh
        ref.read(currentSentStatusProvider.notifier).state = statuses[index];
      },
      tabTitles: [
        context.tr.pending,
        context.tr.confirmed,
        context.tr.pickedUp,
        context.tr.delivered,
      ],
      children: [
        _buildSentOrdersList(context, ref, expandedCardId),
        _buildSentOrdersList(context, ref, expandedCardId),
        _buildSentOrdersList(context, ref, expandedCardId),
        _buildSentOrdersList(context, ref, expandedCardId),
      ],
    );
  }

  Widget _buildReceivedShipmentsTab(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> tabIndex,
    ValueNotifier<String?> expandedCardId,
    List<String> statuses,
  ) {
    return CustomTabBarWidget(
      key: ValueKey('received_${tabIndex.value}'),
      // Force rebuild when tab index changes
      initialIndex: tabIndex.value,
      isScrollable: true,
      color: ColorManager.orangeColor,
      onTabChanged: (index) {
        tabIndex.value = index;
        // Update the current status to trigger refresh
        ref.read(currentReceivedStatusProvider.notifier).state =
            statuses[index];
      },
      tabTitles: [
        context.tr.waitingForOrder,
        context.tr.pickedUpDelivery,
        context.tr.deliveredShipments,
      ],
      children: [
        _buildReceivedOrdersList(context, ref, expandedCardId),
        _buildReceivedOrdersList(context, ref, expandedCardId),
        _buildReceivedOrdersList(context, ref, expandedCardId),
      ],
    );
  }

  Widget _buildSentOrdersList(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<String?> expandedCardId,
  ) {
    final ordersHistoryAsync = ref.watch(getSentOrdersHistoryProvider);

    return ordersHistoryAsync.get(
      data: (ordersResponse) => _buildShipmentsList(
        context,
        ordersResponse.data.created,
        expandedCardId,
        true,
      ),
    );
  }

  Widget _buildReceivedOrdersList(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<String?> expandedCardId,
  ) {
    final ordersHistoryAsync = ref.watch(getReceivedOrdersHistoryProvider);

    return ordersHistoryAsync.get(
      data: (ordersResponse) => _buildShipmentsList(
        context,
        ordersResponse.data.received,
        expandedCardId,
        false,
      ),
    );
  }

  Widget _buildShipmentsList(BuildContext context, List<OrderModel> orders,
      ValueNotifier<String?> expandedCardId, bool showMoney) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              context.tr.noOrdersFound,
              style: AppTextStyles.bodyLarge.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      );
    }

    return BaseList<OrderModel>(
      data: orders,
      padding: EdgeInsets.only(bottom: 10.h),
      itemBuilder: (order, index) => ShipmentCardWidget(
        order: order,
        showMoney: showMoney,
        isExpanded: expandedCardId.value == order.id.toString(),
        onTap: () {
          if (expandedCardId.value == order.id.toString()) {
            expandedCardId.value = null;
          } else {
            expandedCardId.value = order.id.toString();
          }
        },
      ),
    );
  }
}
