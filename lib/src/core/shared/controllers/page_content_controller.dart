import 'package:dropx/src/core/shared/models/page_content_model.dart';
import 'package:dropx/src/core/shared/repositories/page_content_repository.dart';
import 'package:riverpod/riverpod.dart';

class PageContentController
    extends StateNotifier<AsyncValue<PageContentModel?>> {
  final PageContentRepository _repository;

  PageContentController(this._repository) : super(const AsyncValue.data(null));

  // * Get Privacy Policy
  Future<void> getPrivacyPolicy() async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.getPrivacyPolicy();
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // * Get About Us
  Future<void> getAboutUs() async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.getAboutUs();
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // * Get Terms of Service
  Future<void> getTermsOfService() async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.getTermsOfService();
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // * Clear state
  void clear() {
    state = const AsyncValue.data(null);
  }
}
