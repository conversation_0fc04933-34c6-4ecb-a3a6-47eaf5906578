import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:dropx/src/screens/auth/providers/auth_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class NewPasswordFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;
  final String phone;

  const NewPasswordFieldsContainer({
    super.key,
    required this.formKey,
    required this.phone,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final isFormValid = useState(false);
    final passwordValid = useState(false);
    final confirmPasswordValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = passwordValid.value && confirmPasswordValid.value;
      return null;
    }, [passwordValid.value, confirmPasswordValid.value]);

    void resetPassword() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      authController.resetPassword(
        phone: phone,
        password: data[FieldsConsts.password],
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: context.height * 0.12),

            // Message
            Text(
              context.tr.newPasswordMessage,
              textAlign: TextAlign.center,
              style: AppTextStyles.headLine.copyWith(
                fontSize: 22,
                color: Colors.black87,
              ),
            ),

            SizedBox(height: context.height * 0.1),

            // Password Field
            BaseTextField(
              name: FieldsConsts.password,
              title: context.tr.newPassword,
              hint: context.tr.newPassword,
              textInputType: TextInputType.visiblePassword,
              isObscure: true,
              useUnderlineBorder: true,
              validator: (value) => Validations.password(
                value,
                emptyPasswordMessage: context.tr.password,
              ),
              realTimeValidator: (value) {
                final error = Validations.password(
                  value,
                  emptyPasswordMessage: context.tr.password,
                );
                passwordValid.value = error == null;
                return error;
              },
            ),

            AppGaps.gap16,

            // Confirm Password Field
            BaseTextField(
              name: FieldsConsts.confirmPassword,
              title: context.tr.confirmPassword,
              hint: context.tr.confirmPassword,
              textInputType: TextInputType.visiblePassword,
              isObscure: true,
              useUnderlineBorder: true,
              validator: (value) {
                final password =
                    formKey.currentState?.fields[FieldsConsts.password]?.value;
                return Validations.confirmPassword(
                  value,
                  password,
                  emptyMessage: context.tr.confirmPassword,
                  mismatchMessage: context.tr.passwordsDoNotMatch,
                );
              },
              realTimeValidator: (value) {
                final password =
                    formKey.currentState?.fields[FieldsConsts.password]?.value;
                final error = Validations.confirmPassword(
                  value,
                  password,
                  emptyMessage: context.tr.confirmPassword,
                  mismatchMessage: context.tr.passwordsDoNotMatch,
                );
                confirmPasswordValid.value = error == null;
                return error;
              },
            ),

            SizedBox(height: context.height * 0.15),

            // Reset Password Button
            SizedBox(
              height: 50,
              width: double.infinity,
              child: Button(
                onPressed: isFormValid.value && !authController.isLoading
                    ? resetPassword
                    : null,
                label: context.tr.resetPasswordButton,
                isLoading: authController.isLoading,
                loadingWidget: LoadingWidget(),
              ),
            ),

            AppGaps.gap48,
          ],
        ),
      ),
    );
  }
}
