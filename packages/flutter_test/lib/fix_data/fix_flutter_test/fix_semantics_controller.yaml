# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the
# flutter/packages/flutter_test/test_fixes/README.md file for instructions
# on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for the flutter_test/controller.dart file. *

version: 1
transforms:
  # Changes made in TBD
  - title: "Migrate to startNode and endNode."
    date: 2024-02-13
    element:
      uris: [ 'flutter_test.dart' ]
      method: simulatedAccessibilityTraversal
      inClass: SemanticsController
    oneOf:
      - if: "start != '' && end != ''"
        changes:
          - kind: 'addParameter'
            index: 2
            name: 'startNode'
            style: optional_named
            argumentValue:
              expression: '{% start %}'
              requiredIf: "start != '' && end != ''"
          - kind: 'addParameter'
            index: 3
            name: 'endNode'
            style: optional_named
            argumentValue:
              expression: '{% end %}'
              requiredIf: "start != '' && end != ''"
          - kind: 'removeParameter'
            name: 'start'
          - kind: 'removeParameter'
            name: 'end'
      - if: "start != '' && end == ''"
        changes:
          - kind: 'addParameter'
            index: 2
            name: 'startNode'
            style: optional_named
            argumentValue:
              expression: '{% start %}'
              requiredIf: "start != '' && end == ''"
          - kind: 'removeParameter'
            name: 'start'
      - if: "start == '' && end != ''"
        changes:
          - kind: 'addParameter'
            index: 2
            name: 'endNode'
            style: optional_named
            argumentValue:
              expression: '{% end %}'
              requiredIf: "start == '' && end != ''"
          - kind: 'removeParameter'
            name: 'end'
    variables:
      start:
        kind: 'fragment'
        value: 'arguments[start]'
      end:
        kind: 'fragment'
        value: 'arguments[end]'
