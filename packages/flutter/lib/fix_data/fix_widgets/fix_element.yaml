# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for Element from the Widgets library. *
#   For fixes to
#     * Actions: fix_actions.yaml
#     * BuildContext: fix_build_context.yaml
#     * ListWheelScrollView: fix_list_wheel_scroll_view.yaml
#     * Widgets (general): fix_widgets.yaml
version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'dependOnInheritedElement'"
    date: 2020-12-23
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'inheritFromElement'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'dependOnInheritedElement'

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'dependOnInheritedWidgetOfExactType'"
    date: 2020-09-14
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'inheritFromWidgetOfExactType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'dependOnInheritedWidgetOfExactType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0]'
      - kind: 'removeParameter'
        index: 0

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'getElementForInheritedWidgetOfExactType'"
    date: 2020-09-14
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'ancestorInheritedElementForWidgetOfExactType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'getElementForInheritedWidgetOfExactType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0]'
      - kind: 'removeParameter'
        index: 0

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'getElementForInheritedWidgetOfExactType'"
    date: 2020-12-23
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'ancestorWidgetOfExactType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'findAncestorWidgetOfExactType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0]'
      - kind: 'removeParameter'
        index: 0

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'findAncestorStateOfType'"
    date: 2020-12-23
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'ancestorStateOfType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'findAncestorStateOfType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0].typeArguments[0]'
      - kind: 'removeParameter'
        index: 0

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'findAncestorRenderObjectOfType'"
    date: 2020-12-23
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'ancestorRenderObjectOfType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'findAncestorRenderObjectOfType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0].typeArguments[0]'
      - kind: 'removeParameter'
        index: 0

  # Changes made in https://github.com/flutter/flutter/pull/44189.
  - title: "Rename to 'findAncestorRenderObjectOfType'"
    date: 2020-12-23
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'rootAncestorStateOfType'
      inClass: 'Element'
    changes:
      - kind: 'rename'
        newName: 'findRootAncestorStateOfType'
      - kind: 'addTypeParameter'
        index: 0
        name: 'T'
        argumentValue:
          expression: '{% type %}'
          variables:
            type:
              kind: 'fragment'
              value: 'arguments[0].typeArguments[0]'
      - kind: 'removeParameter'
        index: 0

# Before adding a new fix: read instructions at the top of this file.
