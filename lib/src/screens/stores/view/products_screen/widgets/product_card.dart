import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductCard extends StatelessWidget {
  const ProductCard({super.key});

  @override
  Widget build(BuildContext context) {
    final width = 140.w;

    return SizedBox(
      height: 110.h,
      child: Stack(
        children: [
          BaseCachedImage(
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyXeKDN29AmZgZPLS7n0Bepe8QmVappBwZCeA3XWEbWNdiDFB',
            width: width,
            height: 110.h,
            radius: AppRadius.radius20,
          ),
          Container(
            width: width,
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.padding12,
              vertical: AppSpaces.padding8,
            ),
            alignment: Alignment.bottomLeft,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppSpaces.padding20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomLeft,
                          child: Text(
                            'Dogs Food',
                            style: AppTextStyles.title
                                .copyWith(color: Colors.white),
                          ),
                        ),
                      ),
                      Text(
                        '\$20',
                        style: AppTextStyles.title
                            .copyWith(color: ColorManager.primaryColor),
                      ),
                    ],
                  ),
                ),
                CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  radius: 14.r,
                  child: const Icon(
                    Icons.add_shopping_cart_outlined,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
