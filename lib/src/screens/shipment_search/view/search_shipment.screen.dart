import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/drop_downs/station_dropdown.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/shipment_search/models/search_request.model.dart';
import 'package:dropx/src/screens/shipment_search/view/search_results.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class SearchShipmentScreen extends HookConsumerWidget {
  const SearchShipmentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fromStation = useState<Station?>(null);
    final toStation = useState<Station?>(null);
    final isLoading = useState(false);

    void searchShipments() {
      // Validate stations
      if (fromStation.value == null) {
        showToast(context.tr.pleaseSelectFromStation, isError: true);
        return;
      }

      if (toStation.value == null) {
        showToast(context.tr.pleaseSelectToStation, isError: true);
        return;
      }

      if (fromStation.value!.id == toStation.value!.id) {
        showToast(context.tr.cannotSelectSameStationForSearch, isError: true);
        return;
      }

      // Navigate to search results
      final searchRequest = SearchRequest(
        fromStationId: fromStation.value!.id,
        toStationId: toStation.value!.id,
      );

      SearchResultsScreen(
        searchRequest: searchRequest,
        fromStation: fromStation.value!,
        toStation: toStation.value!,
      ).navigate;
    }

    return Scaffold(
      backgroundColor: ColorManager.white,
      appBar: AppBar(
        title: Text(
          context.tr.searchShipmentTitle,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // From Station
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: ColorManager.primaryColor,
                  size: 24,
                ),
                AppGaps.gap8,
                Text(
                  context.tr.fromStation,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            AppGaps.gap8,
            StationDropdown(
              label: context.tr.fromStation,
              selectedStation: fromStation,
              icon: const Icon(
                Icons.location_on,
                color: ColorManager.primaryColor,
              ),
            ),
            AppGaps.gap24,

            // To Station
            Row(
              children: [
                const Icon(
                  Icons.local_shipping,
                  color: ColorManager.primaryColor,
                  size: 24,
                ),
                AppGaps.gap8,
                Text(
                  context.tr.toStation,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            AppGaps.gap8,
            StationDropdown(
              label: context.tr.toStation,
              selectedStation: toStation,
              icon: const Icon(
                Icons.local_shipping,
                color: ColorManager.primaryColor,
              ),
            ),

            const Spacer(),

            // Search Button
            Button(
              label: context.tr.searchShipmentButton,
              onPressed: searchShipments,
              isLoading: isLoading.value,
              color: ColorManager.primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
