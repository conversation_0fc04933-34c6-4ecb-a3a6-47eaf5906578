import 'package:equatable/equatable.dart';

class OrderType extends Equatable {
  final int id;
  final String name;

  const OrderType({
    required this.id,
    this.name = '',
  });

  factory OrderType.fromJson(Map<String, dynamic> json) {
    return OrderType(
      id: json['id'],
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
      ];

  @override
  String toString() => name;
}
