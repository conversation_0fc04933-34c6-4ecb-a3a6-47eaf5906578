import 'package:equatable/equatable.dart';

class WalletInfoModel extends Equatable {
  final int balance;
  final int totalEarned;
  final int totalSpent;
  final int transactionsCount;

  const WalletInfoModel({
    required this.balance,
    required this.totalEarned,
    required this.totalSpent,
    required this.transactionsCount,
  });

  factory WalletInfoModel.fromJson(Map<String, dynamic> json) {
    return WalletInfoModel(
      balance: json['balance'] ?? 0,
      totalEarned: json['total_earned'] ?? 0,
      totalSpent: json['total_spent'] ?? 0,
      transactionsCount: json['transactions_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'balance': balance,
      'total_earned': totalEarned,
      'total_spent': totalSpent,
      'transactions_count': transactionsCount,
    };
  }

  @override
  List<Object?> get props => [
        balance,
        totalEarned,
        totalSpent,
        transactionsCount,
      ];
}

class WalletInfoResponse extends Equatable {
  final WalletInfoModel data;

  const WalletInfoResponse({
    required this.data,
  });

  factory WalletInfoResponse.fromJson(Map<String, dynamic> json) {
    return WalletInfoResponse(
      data: WalletInfoModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  @override
  List<Object?> get props => [data];
}
