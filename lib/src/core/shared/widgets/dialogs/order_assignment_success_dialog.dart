import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class OrderAssignmentSuccessDialog extends StatelessWidget {
  final String barcode;
  final String orderType;

  const OrderAssignmentSuccessDialog({
    super.key,
    required this.barcode,
    required this.orderType,
  });

  static Future<void> show({
    required BuildContext context,
    required String barcode,
    required String orderType,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => OrderAssignmentSuccessDialog(
        barcode: barcode,
        orderType: orderType,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Success Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 50,
              ),
            ),
            AppGaps.gap16,

            // Title
            Text(
              context.tr.orderAssignedSuccessfully,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.bold,
                color: ColorManager.primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap16,

            // Barcode Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ColorManager.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    context.tr.barcodeNumber,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  AppGaps.gap8,
                  Text(
                    barcode,
                    style: AppTextStyles.title.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      color: ColorManager.primaryColor,
                      letterSpacing: 2,
                    ),
                  ),
                ],
              ),
            ),
            AppGaps.gap16,

            // Order Type
            Text(
              '${context.tr.orderType}: $orderType',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            AppGaps.gap24,

            // Success Message
            Text(
              context.tr.orderAssignmentSuccessMessage,
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),

            AppGaps.gap24,

            // OK Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  context.tr.ok,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
