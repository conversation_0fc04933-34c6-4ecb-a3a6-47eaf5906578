import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:dropx/src/core/shared/widgets/tabs/custom_tab_bar.widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../widgets/shipment_card.widget.dart';

class HomeScreen extends HookConsumerWidget {
  final int? homeCurrentIndex;

  const HomeScreen({super.key, this.homeCurrentIndex});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ordersAsync = ref.watch(getOrdersFutureProvider);
    final expandedCardId = useState<int?>(null);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          UserModel.currentUser().name ?? '',
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.only(
            right: AppSpaces.padding8,
            bottom: AppSpaces.padding8,
            top: AppSpaces.padding8,
          ),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Active Shipments Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.screenPadding,
              vertical: 16.h,
            ),
            child: Text(
              context.tr.activeShipments,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Custom Tab Bar with Hook
          Expanded(
            child: CustomTabBarWidget(
              initialIndex: homeCurrentIndex,
              tabTitles: [
                context.tr.mySentShipments,
                context.tr.myReceivedShipments,
              ],
              children: [
                // Received Shipments Tab
                ordersAsync.get(
                  data: (ordersResponse) => _buildShipmentsList(
                    context,
                    ordersResponse.data.created,
                    context.tr.noReceivedShipments,
                    expandedCardId,
                    ref,
                    isFirstTab: true,
                  ),
                ),
                // Sent Shipments Tab
                ordersAsync.get(
                  data: (ordersResponse) => _buildShipmentsList(
                    context,
                    ordersResponse.data.received,
                    context.tr.noSentShipments,
                    expandedCardId,
                    ref,
                    isFirstTab: false,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentsList(BuildContext context, List<OrderModel> orders,
      String emptyMessage, ValueNotifier<int?> expandedCardId, WidgetRef ref,
      {required bool isFirstTab}) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              emptyMessage,
              style: AppTextStyles.bodyLarge.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      );
    }

    return BaseList<OrderModel>(
      data: orders,
      padding: EdgeInsets.only(bottom: 10.h),
      itemBuilder: (order, index) => ShipmentCardWidget(
        order: order,
        showMoney: isFirstTab,
        isExpanded: expandedCardId.value == order.id,
        onTap: () {
          if (expandedCardId.value == order.id) {
            expandedCardId.value = null;
          } else {
            expandedCardId.value = order.id;
          }
        },
      ),
    );
  }
}
