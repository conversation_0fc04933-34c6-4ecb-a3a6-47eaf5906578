import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/wallet/models/wallet_info.model.dart';
import 'package:dropx/src/screens/wallet/models/wallet_transaction.model.dart';
import 'package:xr_helper/xr_helper.dart';

class WalletRepository with BaseRepository {
  final BaseApiServices networkApiService;

  WalletRepository({
    required this.networkApiService,
  });

  // * Get Wallet Info
  Future<WalletInfoModel> getWalletInfo() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.wallet;

        final response = await networkApiService.getResponse(url);

        final walletInfoResponse = WalletInfoResponse.fromJson(response);

        return walletInfoResponse.data;
      },
    );
  }

  // * Get Wallet Transactions
  Future<WalletTransactionsResponse> getWalletTransactions({
    int page = 1,
    String? type,
  }) async {
    return baseFunction(
      () async {
        String url = '${ApiEndpoints.walletTransactions}';

        if (type != null && type.isNotEmpty) {
          url += '?type=$type';
        }

        final response = await networkApiService.getResponse(url);

        final walletTransactionsResponse =
            WalletTransactionsResponse.fromJson(response);

        return walletTransactionsResponse;
      },
    );
  }
}
