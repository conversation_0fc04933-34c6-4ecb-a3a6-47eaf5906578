part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatDateToStringWithTime {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd hh:mm:ss', 'en').format(this!);
  }

  bool isToday() {
    if (this == null) return false;
    final now = DateTime.now();
    return now.year == this!.year &&
        now.month == this!.month &&
        now.day == this!.day;
  }
}
