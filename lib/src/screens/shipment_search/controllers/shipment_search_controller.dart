import 'package:dropx/src/screens/shipment_search/models/delivery_search_response.model.dart';
import 'package:dropx/src/screens/shipment_search/models/search_request.model.dart';
import 'package:dropx/src/screens/shipment_search/repositories/shipment_search_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ShipmentSearchController extends BaseVM {
  final ShipmentSearchRepository shipmentSearchRepo;

  ShipmentSearchController({
    required this.shipmentSearchRepo,
  });

  // * Search Delivery
  Future<DeliverySearchResponse> searchDelivery({
    required SearchRequest searchRequest,
  }) async {
    return await baseFunction(
      () async {
        return await shipmentSearchRepo.searchDelivery(searchRequest: searchRequest);
      },
    );
  }

  // * Add Attendance
  Future<bool> addAttendance({
    required AttendanceRequest attendanceRequest,
  }) async {
    return await baseFunction(
      () async {
        return await shipmentSearchRepo.addAttendance(attendanceRequest: attendanceRequest);
      },
    );
  }

  // * Delete Attendance
  Future<bool> deleteAttendance() async {
    return await baseFunction(
      () async {
        return await shipmentSearchRepo.deleteAttendance();
      },
    );
  }

  // * Add Interest
  Future<bool> addInterest({
    required InterestRequest interestRequest,
  }) async {
    return await baseFunction(
      () async {
        return await shipmentSearchRepo.addInterest(interestRequest: interestRequest);
      },
    );
  }
}
