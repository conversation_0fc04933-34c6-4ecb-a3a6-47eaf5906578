import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dropx/generated/assets.gen.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseHeaderWidget extends StatelessWidget {
  final String? title;
  final bool withBackButton;

  const BaseHeaderWidget({
    super.key,
    this.title,
    this.withBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(
        statusBarColor: ColorManager.primaryColor,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Stack(
        children: [
          // Background with curved design
          Assets.images.topClipper.image(
            width: double.infinity,
            fit: BoxFit.cover,
          ),
          Safe<PERSON><PERSON>(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 24.0,
                vertical: Platform.isIOS ? 12.0 : context.height * 0.045,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (withBackButton)
                        IconButton(
                          onPressed: () => context.back(),
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                      if (title != null)
                        Text(
                          title!,
                          style: AppTextStyles.title.copyWith(
                            color: ColorManager.white,
                            fontSize: withBackButton ? 22 : 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                  Assets.images.logoSymbol.image(
                    width: 50,
                    height: 50,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.account_balance,
                        color: ColorManager.white,
                        size: 50,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
