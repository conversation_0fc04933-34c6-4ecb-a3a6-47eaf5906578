import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/wallet_controller.dart';
import '../repositories/wallet_repository.dart';

// * Wallet Repo Provider ========================================
final walletRepoProvider = Provider<WalletRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return WalletRepository(networkApiService: networkApiService);
});

// * Wallet Controller Provider ========================================
final walletControllerProvider = ChangeNotifierProvider<WalletController>(
  (ref) {
    final walletRepo = ref.watch(walletRepoProvider);

    return WalletController(
      walletRepo: walletRepo,
    );
  },
);
