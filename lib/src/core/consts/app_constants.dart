import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:dropx/generated/l10n.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:xr_helper/xr_helper.dart';

class AppConsts {
  static const String appName = 'DropX';
  static const Locale locale = Locale('en');

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    locale,
  ];

  static bool get isEnglish =>
      GetStorageService.getData(key: LocalKeys.language) == 'en';

  static final fontFamily = GoogleFonts.cairo().fontFamily;

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  //? Test Login
  static const String testEmail = 'admin';
  static const String testPass = 'test@123';
}
