import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/stores/models/store.model.dart';
import 'package:xr_helper/xr_helper.dart';

class StoresRepository with BaseRepository {
  final BaseApiServices networkApiService;

  StoresRepository({
    required this.networkApiService,
  });

  // * Get Stores
  Future<List<StoreModel>> getStores() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.stores;

        final response = await networkApiService.getResponse(url);

        final stores = response['data'] as List;

        final storesList =
            stores.map((country) => StoreModel.fromJson(country)).toList();

        return storesList;
      },
    );
  }

  // * Add Stores
  Future<void> addStores({
    required Map<String, dynamic> data,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.stores;

        await networkApiService.postResponse(
          url,
          body: data,
        );
      },
    );
  }
}
