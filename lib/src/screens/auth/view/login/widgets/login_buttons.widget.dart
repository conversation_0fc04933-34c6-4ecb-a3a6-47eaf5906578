import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/view/register/register.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/loading/loading_widget.dart';

class LoginButtons extends StatelessWidget {
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onLogin;

  const LoginButtons({
    super.key,
    required this.isLoading,
    required this.isFormValid,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Login Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: Button(
            onPressed: isFormValid && !isLoading ? onLogin : null,
            isLoading: isLoading,
            loadingWidget: LoadingWidget(),
            label: context.tr.login,
          ),
        ),

        AppGaps.gap16,

        // Don't Have Account Text
        Text(
          context.tr.dontHaveAccount,
          textAlign: TextAlign.center,
          style: AppTextStyles.labelMedium.copyWith(
            color: Colors.black54,
            fontSize: 14,
          ),
        ),

        AppGaps.gap8,

        // Register Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              const RegisterScreen().navigate;
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: ColorManager.primaryColor,
              side: const BorderSide(
                color: ColorManager.primaryColor,
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              context.tr.createAccount,
              style: AppTextStyles.labelLarge.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorManager.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
