import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/html_content_dialog.dart';

class TermsCheckbox extends HookWidget {
  final ValueNotifier<bool> termsAccepted;

  const TermsCheckbox({
    super.key,
    required this.termsAccepted,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: SizedBox(
        width: 24,
        height: 24,
        child: Checkbox(
          value: termsAccepted.value,
          checkColor: ColorManager.white,
          onChanged: (bool? value) {
            termsAccepted.value = value ?? false;
          },
          activeColor: ColorManager.primaryColor,
          side: const BorderSide(color: Colors.black, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
      title: RichText(
        text: TextSpan(
          style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),
          children: [
            TextSpan(text: context.tr.termsAndConditionsText),
            const TextSpan(text: ' '),
            TextSpan(
              text: context.tr.termsAndConditionsLink,
              style: const TextStyle(
                color: ColorManager.termsLinkColor,
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  HtmlContentDialog.show(context, PageType.termsOfService);
                },
            ),
            const TextSpan(text: ' '),
            TextSpan(text: context.tr.termsAndConditionsEnd),
          ],
        ),
      ),
      onTap: () {
        termsAccepted.value = !termsAccepted.value;
      },
    );
  }
}
