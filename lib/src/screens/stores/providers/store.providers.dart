import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/providers/network_api_service_provider.dart';
import 'package:dropx/src/screens/stores/controllers/store.controller.dart';
import 'package:dropx/src/screens/stores/repositories/store.repository.dart';

// * Stores Repo Provider ========================================
final storesRepoProvider = Provider<StoresRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return StoresRepository(networkApiService: networkApiService);
});

// * Stores Change Notifier Provider ========================================
final storesControllerNotifierProvider =
    ChangeNotifierProvider<StoresController>(
  (ref) {
    final storesRepo = ref.watch(storesRepoProvider);

    return StoresController(
      storesRepo: storesRepo,
    );
  },
);

// * Stores Provider ========================================
final storesControllerProvider = Provider<StoresController>(
  (ref) {
    final storesRepo = ref.watch(storesRepoProvider);

    return StoresController(
      storesRepo: storesRepo,
    );
  },
);

// * Get Academic Stages Future Provider ========================================
final getStoresFutureProvider = FutureProvider(
  (ref) {
    final storesController = ref.watch(storesControllerProvider);

    return storesController.getStores();
  },
);
