import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';

class BaseImagePicker extends StatelessWidget {
  final String name;
  final String? label;
  final int maxImages;
  final bool isRequired;
  final List<dynamic>? initialValue;
  final Function(List<dynamic>?)? onChanged;

  const BaseImagePicker({
    super.key,
    required this.name,
    this.label,
    this.maxImages = 1,
    this.isRequired = true,
    this.initialValue,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return FormBuilderImagePicker(
      name: name,
      fit: BoxFit.cover,
      imageQuality: 70,
      loadingWidget: (context) => const LoadingWidget(),
      placeholderWidget: const Icon(
        CupertinoIcons.photo,
        size: 100,
        color: ColorManager.primaryColor,
      ),
      decoration: InputDecoration(
        labelText: label ?? context.tr.pickImage,
        border: const OutlineInputBorder(),
      ),
      maxImages: maxImages,
      initialValue: initialValue ?? [],
      validator: isRequired
          ? FormBuilderValidators.compose([
              FormBuilderValidators.required(),
            ])
          : null,
      onChanged: onChanged,
    );
  }
}
