// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// The Flutter widgets framework.
///
/// To use, import `package:flutter/widgets.dart`.
///
/// See also:
///
///  * [flutter.dev/widgets](https://flutter.dev/widgets/)
///    for a catalog of commonly-used Flutter widgets.
library widgets;

export 'package:characters/characters.dart';
export 'package:vector_math/vector_math_64.dart' show Matrix4;

export 'foundation.dart' show Brightness, UniqueKey;
export 'rendering.dart' show TextSelectionHandleType;
export 'src/widgets/actions.dart';
export 'src/widgets/adapter.dart';
export 'src/widgets/animated_cross_fade.dart';
export 'src/widgets/animated_scroll_view.dart';
export 'src/widgets/animated_size.dart';
export 'src/widgets/animated_switcher.dart';
export 'src/widgets/annotated_region.dart';
export 'src/widgets/app.dart';
export 'src/widgets/app_lifecycle_listener.dart';
export 'src/widgets/async.dart';
export 'src/widgets/autocomplete.dart';
export 'src/widgets/autofill.dart';
export 'src/widgets/automatic_keep_alive.dart';
export 'src/widgets/banner.dart';
export 'src/widgets/basic.dart';
export 'src/widgets/binding.dart';
export 'src/widgets/bottom_navigation_bar_item.dart';
export 'src/widgets/color_filter.dart';
export 'src/widgets/container.dart';
export 'src/widgets/context_menu_button_item.dart';
export 'src/widgets/context_menu_controller.dart';
export 'src/widgets/debug.dart';
export 'src/widgets/decorated_sliver.dart';
export 'src/widgets/default_selection_style.dart';
export 'src/widgets/default_text_editing_shortcuts.dart';
export 'src/widgets/desktop_text_selection_toolbar_layout_delegate.dart';
export 'src/widgets/dismissible.dart';
export 'src/widgets/display_feature_sub_screen.dart';
export 'src/widgets/disposable_build_context.dart';
export 'src/widgets/drag_boundary.dart';
export 'src/widgets/drag_target.dart';
export 'src/widgets/draggable_scrollable_sheet.dart';
export 'src/widgets/dual_transition_builder.dart';
export 'src/widgets/editable_text.dart';
export 'src/widgets/expansible.dart';
export 'src/widgets/fade_in_image.dart';
export 'src/widgets/feedback.dart';
export 'src/widgets/flutter_logo.dart';
export 'src/widgets/focus_manager.dart';
export 'src/widgets/focus_scope.dart';
export 'src/widgets/focus_traversal.dart';
export 'src/widgets/form.dart';
export 'src/widgets/framework.dart';
export 'src/widgets/gesture_detector.dart';
export 'src/widgets/grid_paper.dart';
export 'src/widgets/heroes.dart';
export 'src/widgets/icon.dart';
export 'src/widgets/icon_data.dart';
export 'src/widgets/icon_theme.dart';
export 'src/widgets/icon_theme_data.dart';
export 'src/widgets/image.dart';
export 'src/widgets/image_filter.dart';
export 'src/widgets/image_icon.dart';
export 'src/widgets/implicit_animations.dart';
export 'src/widgets/inherited_model.dart';
export 'src/widgets/inherited_notifier.dart';
export 'src/widgets/inherited_theme.dart';
export 'src/widgets/interactive_viewer.dart';
export 'src/widgets/keyboard_listener.dart';
export 'src/widgets/layout_builder.dart';
export 'src/widgets/list_wheel_scroll_view.dart';
export 'src/widgets/localizations.dart';
export 'src/widgets/lookup_boundary.dart';
export 'src/widgets/magnifier.dart';
export 'src/widgets/media_query.dart';
export 'src/widgets/modal_barrier.dart';
export 'src/widgets/navigation_toolbar.dart';
export 'src/widgets/navigator.dart';
export 'src/widgets/navigator_pop_handler.dart';
export 'src/widgets/nested_scroll_view.dart';
export 'src/widgets/notification_listener.dart';
export 'src/widgets/orientation_builder.dart';
export 'src/widgets/overflow_bar.dart';
export 'src/widgets/overlay.dart';
export 'src/widgets/overscroll_indicator.dart';
export 'src/widgets/page_storage.dart';
export 'src/widgets/page_view.dart';
export 'src/widgets/pages.dart';
export 'src/widgets/performance_overlay.dart';
export 'src/widgets/pinned_header_sliver.dart';
export 'src/widgets/placeholder.dart';
export 'src/widgets/platform_menu_bar.dart';
export 'src/widgets/platform_selectable_region_context_menu.dart';
export 'src/widgets/platform_view.dart';
export 'src/widgets/pop_scope.dart';
export 'src/widgets/preferred_size.dart';
export 'src/widgets/primary_scroll_controller.dart';
export 'src/widgets/radio_group.dart';
export 'src/widgets/raw_keyboard_listener.dart';
export 'src/widgets/raw_menu_anchor.dart';
export 'src/widgets/raw_radio.dart';
export 'src/widgets/reorderable_list.dart';
export 'src/widgets/restoration.dart';
export 'src/widgets/restoration_properties.dart';
export 'src/widgets/router.dart';
export 'src/widgets/routes.dart';
export 'src/widgets/safe_area.dart';
export 'src/widgets/scroll_activity.dart';
export 'src/widgets/scroll_aware_image_provider.dart';
export 'src/widgets/scroll_configuration.dart';
export 'src/widgets/scroll_context.dart';
export 'src/widgets/scroll_controller.dart';
export 'src/widgets/scroll_delegate.dart';
export 'src/widgets/scroll_metrics.dart';
export 'src/widgets/scroll_notification.dart';
export 'src/widgets/scroll_notification_observer.dart';
export 'src/widgets/scroll_physics.dart';
export 'src/widgets/scroll_position.dart';
export 'src/widgets/scroll_position_with_single_context.dart';
export 'src/widgets/scroll_simulation.dart';
export 'src/widgets/scroll_view.dart';
export 'src/widgets/scrollable.dart';
export 'src/widgets/scrollable_helpers.dart';
export 'src/widgets/scrollbar.dart';
export 'src/widgets/selectable_region.dart';
export 'src/widgets/selection_container.dart';
export 'src/widgets/semantics_debugger.dart';
export 'src/widgets/sensitive_content.dart';
export 'src/widgets/service_extensions.dart';
export 'src/widgets/shared_app_data.dart';
export 'src/widgets/shortcuts.dart';
export 'src/widgets/single_child_scroll_view.dart';
export 'src/widgets/size_changed_layout_notifier.dart';
export 'src/widgets/sliver.dart';
export 'src/widgets/sliver_fill.dart';
export 'src/widgets/sliver_floating_header.dart';
export 'src/widgets/sliver_layout_builder.dart';
export 'src/widgets/sliver_persistent_header.dart';
export 'src/widgets/sliver_prototype_extent_list.dart';
export 'src/widgets/sliver_resizing_header.dart';
export 'src/widgets/sliver_tree.dart';
export 'src/widgets/slotted_render_object_widget.dart';
export 'src/widgets/snapshot_widget.dart';
export 'src/widgets/spacer.dart';
export 'src/widgets/spell_check.dart';
export 'src/widgets/standard_component_type.dart';
export 'src/widgets/status_transitions.dart';
export 'src/widgets/system_context_menu.dart';
export 'src/widgets/table.dart';
export 'src/widgets/tap_region.dart';
export 'src/widgets/text.dart';
export 'src/widgets/text_editing_intents.dart';
export 'src/widgets/text_selection.dart';
export 'src/widgets/text_selection_toolbar_anchors.dart';
export 'src/widgets/text_selection_toolbar_layout_delegate.dart';
export 'src/widgets/texture.dart';
export 'src/widgets/ticker_provider.dart';
export 'src/widgets/title.dart';
export 'src/widgets/toggleable.dart';
export 'src/widgets/transitions.dart';
export 'src/widgets/tween_animation_builder.dart';
export 'src/widgets/two_dimensional_scroll_view.dart';
export 'src/widgets/two_dimensional_viewport.dart';
export 'src/widgets/undo_history.dart';
export 'src/widgets/unique_widget.dart';
export 'src/widgets/value_listenable_builder.dart';
export 'src/widgets/view.dart';
export 'src/widgets/viewport.dart';
export 'src/widgets/visibility.dart';
export 'src/widgets/widget_inspector.dart';
export 'src/widgets/widget_span.dart';
export 'src/widgets/widget_state.dart';
export 'src/widgets/will_pop_scope.dart';
