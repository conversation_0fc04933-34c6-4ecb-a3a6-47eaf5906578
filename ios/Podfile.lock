PODS:
  - audio_waveforms (0.0.1):
    - Flutter
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - Firebase/CoreOnly (12.2.0):
    - FirebaseCore (~> 12.2.0)
  - Firebase/Messaging (12.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 12.2.0)
  - firebase_core (4.1.0):
    - Firebase/CoreOnly (= 12.2.0)
    - Flutter
  - firebase_messaging (16.0.1):
    - Firebase/Messaging (= 12.2.0)
    - firebase_core
    - Flutter
  - FirebaseCore (12.2.0):
    - FirebaseCoreInternal (~> 12.2.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (12.2.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (12.2.0):
    - FirebaseCore (~> 12.2.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (12.2.0):
    - FirebaseCore (~> 12.2.0)
    - FirebaseInstallations (~> 12.2.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  audio_waveforms: cd736909ebc6b6a164eb74701d8c705ce3241e1c
  emoji_picker_flutter: 8e50ec5caac456a23a78637e02c6293ea0ac8771
  Firebase: 26f6f8d460603af3df970ad505b16b15f5e2e9a1
  firebase_core: 3b9065a1037f7f2e360077d2aa5fb6ec9adb3005
  firebase_messaging: bd5fd047fb48ca5b8010c35e9ac85fe436d363f2
  FirebaseCore: 311c48a147ad4a0ab7febbaed89e8025c67510cd
  FirebaseCoreInternal: 56ea29f3dad2894f81b060f706f9d53509b6ed3b
  FirebaseInstallations: 3e884b01feabdf67582a80f3250425a00979b4ed
  FirebaseMessaging: 43ec73bbfedd0c385a849bb91593ab4ad4b9e48e
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  flutter_native_splash: df59bb2e1421aa0282cb2e95618af4dcb0c56c29
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3

PODFILE CHECKSUM: 9c46fd01abff66081b39f5fa5767b3f1d0b11d76

COCOAPODS: 1.16.2
