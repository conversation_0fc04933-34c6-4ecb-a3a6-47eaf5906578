import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/shipment_search_controller.dart';
import '../repositories/shipment_search_repository.dart';
import '../models/search_request.model.dart';

// * Shipment Search Repo Provider ========================================
final shipmentSearchRepoProvider = Provider<ShipmentSearchRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ShipmentSearchRepository(networkApiService: networkApiService);
});

// * Shipment Search Controller Provider ========================================
final shipmentSearchControllerProvider = Provider<ShipmentSearchController>(
  (ref) {
    final shipmentSearchRepo = ref.watch(shipmentSearchRepoProvider);

    return ShipmentSearchController(
      shipmentSearchRepo: shipmentSearchRepo,
    );
  },
);

// * Search Delivery Future Provider ========================================
final searchDeliveryFutureProvider = FutureProvider.family(
  (ref, SearchRequest searchRequest) {
    final shipmentSearchController = ref.watch(shipmentSearchControllerProvider);

    return shipmentSearchController.searchDelivery(searchRequest: searchRequest);
  },
);

// * Add Attendance Future Provider ========================================
final addAttendanceFutureProvider = FutureProvider.family(
  (ref, AttendanceRequest attendanceRequest) {
    final shipmentSearchController = ref.watch(shipmentSearchControllerProvider);

    return shipmentSearchController.addAttendance(attendanceRequest: attendanceRequest);
  },
);

// * Delete Attendance Future Provider ========================================
final deleteAttendanceFutureProvider = FutureProvider(
  (ref) {
    final shipmentSearchController = ref.watch(shipmentSearchControllerProvider);

    return shipmentSearchController.deleteAttendance();
  },
);

// * Add Interest Future Provider ========================================
final addInterestFutureProvider = FutureProvider.family(
  (ref, InterestRequest interestRequest) {
    final shipmentSearchController = ref.watch(shipmentSearchControllerProvider);

    return shipmentSearchController.addInterest(interestRequest: interestRequest);
  },
);
