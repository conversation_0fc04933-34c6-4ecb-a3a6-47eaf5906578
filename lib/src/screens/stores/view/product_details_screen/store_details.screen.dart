import 'package:dropx/src/screens/stores/view/product_details_screen/widgets/store_top_section_details.widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class StoreDetailsScreen extends StatelessWidget {
  const StoreDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // * Store Top Section (Image - Fav - Back Arrow)
          const StoreTopSectionDetailsWidget(),

          AppGaps.gap24,

          // * Store Tabs
          Expanded(
            child: ListView(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppSpaces.padding12),
              children: [
                // about the store
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // * About
                    Column(
                      children: [
                        Container(
                          width: 80,
                          height: 60.h,
                          decoration: BoxDecoration(
                            color: ColorManager.primaryColor.withOpacity(.8),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius20),
                          ),
                          child: const Icon(
                            CupertinoIcons.info,
                            color: ColorManager.white,
                            size: 40,
                          ),
                        ),
                        AppGaps.gap8,
                        Text(
                          context.tr.about,
                          style: AppTextStyles.title.copyWith(
                            fontSize: 16,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),

                    // * Products
                    Column(
                      children: [
                        Container(
                          width: 80,
                          height: 60.h,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ColorManager.primaryColor.withOpacity(.8),
                              width: 2,
                            ),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius20),
                          ),
                          child: const Icon(
                            Icons.shopping_bag_outlined,
                            color: ColorManager.primaryColor,
                            size: 40,
                          ),
                        ),
                        AppGaps.gap8,
                        Text(
                          context.tr.products,
                          style: AppTextStyles.title.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.normal,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),

                    // * Reels
                    Column(
                      children: [
                        Container(
                          width: 80,
                          height: 60.h,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ColorManager.primaryColor.withOpacity(.8),
                              width: 2,
                            ),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius20),
                          ),
                          child: const Icon(
                            Icons.video_collection_outlined,
                            color: ColorManager.primaryColor,
                            size: 40,
                          ),
                        ),
                        AppGaps.gap8,
                        Text(
                          'Reels',
                          style: AppTextStyles.title.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.normal,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),

                    // * Animals
                    Column(
                      children: [
                        Container(
                          width: 80,
                          height: 60.h,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ColorManager.primaryColor.withOpacity(.8),
                              width: 2,
                            ),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius20),
                          ),
                          child: const Icon(
                            Icons.pets_outlined,
                            color: ColorManager.primaryColor,
                            size: 40,
                          ),
                        ),
                        AppGaps.gap8,
                        Text(
                          context.tr.animals,
                          style: AppTextStyles.title.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.normal,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
