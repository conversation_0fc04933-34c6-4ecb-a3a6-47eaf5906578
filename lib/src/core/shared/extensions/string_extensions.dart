extension StringExtensions on String {
  String get translateErrorMessage {
    //?{"email":"Client Not Found"}

    final errorMessage = this;

    if (!errorMessage.contains(':')) return errorMessage;

    final errorMessageSplitted = errorMessage.split(':');

    final errorKey =
        errorMessageSplitted[0].replaceAll('{', '').replaceAll('"', '');

    final errorValue =
        errorMessageSplitted[1].replaceAll('}', '').replaceAll('"', '');

    return '$errorKey: $errorValue';
  }
}
