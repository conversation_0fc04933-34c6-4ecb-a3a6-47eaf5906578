part of xr_helper;

class Validations {
  //! التحقق من كلمة المرور
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "كلمة المرور لا يمكن أن تكون فارغة",
    String? passwordLengthMessage = "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! التحقق من رقم الهاتف
  static String? phoneNumber(
    value, {
    String? emptyPhoneMessage = "رقم الهاتف لا يمكن أن يكون فارغًا",
    String? phoneLengthMessage = "رقم الهاتف غير صالح",
  }) {
    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return phoneLengthMessage;
    }
    return null;
  }

  //! التحقق من الأرقام فقط
  static String? numbersOnly(
    value, {
    String? emptyMessage = "الحقل لا يمكن أن يكون فارغًا",
    String? invalidMessage = "الرقم غير صالح",
  }) {
    String pattern = r'(^[0-9]*$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return invalidMessage;
    }
    return null;
  }

  //! التحقق من البريد الإلكتروني
  static String? email(
    String? value, {
    String? emptyEmailMessage = "البريد الإلكتروني لا يمكن أن يكون فارغًا",
    String? invalidEmailMessage = "البريد الإلكتروني غير صالح",
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return invalidEmailMessage;
    }
    return null;
  }

  //! التحقق من أن الحقل غير فارغ
  static String? mustBeNotEmpty(String? value, {String? emptyMessage}) {
    if (value == null || value.isEmpty) {
      return emptyMessage ?? "الحقل لا يمكن أن يكون فارغًا";
    }
    return null;
  }

  //! التحقق من الرقم الوطني الفلسطيني
  static String? palestinianId(
    String? value, {
    String? emptyMessage = "رقم الهوية لا يمكن أن يكون فارغًا",
    String? invalidMessage = "رقم الهوية غير صالح",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value.length != 9 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
      return invalidMessage;
    }

    int sum = 0;
    for (int i = 0; i < value.length; i++) {
      int digit = int.parse(value[i]);
      int incNum = digit * ((i % 2) + 1);
      sum += (incNum > 9) ? incNum - 9 : incNum;
    }

    if (sum % 10 != 0) {
      return invalidMessage;
    }

    return null;
  }

  //! التحقق من رقم الهاتف الفلسطيني
  static String? palestinianPhoneNumber(
    String? value, {
    String? emptyMessage = "رقم الهاتف لا يمكن أن يكون فارغًا",
    String? invalidMessage = "رقم الهاتف غير صالح",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    // أنماط أرقام الهواتف الفلسطينية
    List<String> patterns = [
      r'^(00972|0|\+972)[5][0-9]{8}$', // موبايل مع كود 972
      r'^(00970|0|\+970)[5][0-9]{8}$', // موبايل مع كود 970
      r'^(05[0-9]|0[12346789])([0-9]{7})$', // محلي
      r'^(00972|0|\+972|0|)[2][0-9]{7}$', // خط أرضي
    ];

    bool isValid = false;
    for (String pattern in patterns) {
      if (RegExp(pattern).hasMatch(value)) {
        isValid = true;
        break;
      }
    }

    if (!isValid) {
      return invalidMessage;
    }

    return null;
  }

  //! التحقق من تأكيد كلمة المرور
  static String? confirmPassword(
    String? value,
    String? originalPassword, {
    String? emptyMessage = "تأكيد كلمة المرور لا يمكن أن يكون فارغًا",
    String? mismatchMessage = "كلمتا المرور غير متطابقتين",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value != originalPassword) {
      return mismatchMessage;
    }

    return null;
  }
}
