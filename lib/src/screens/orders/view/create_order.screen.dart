import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/app_bar/base_header.widget.dart';
import 'package:dropx/src/screens/orders/view/widgets/create_order_form.widget.dart';

class CreateOrderScreen extends HookConsumerWidget {
  const CreateOrderScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: FormBuilder(
          key: formKey,
          child: Column(
            children: [
              // Header
              BaseHeaderWidget(
                title: context.tr.createOrderTitle,
                withBackButton: true,
              ),

              // Form Content
              Expanded(
                child: CreateOrderFormWidget(
                  formKey: formKey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
