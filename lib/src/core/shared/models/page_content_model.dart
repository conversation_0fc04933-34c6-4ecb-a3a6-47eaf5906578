class PageContentModel {
  final int id;
  final String title;
  final String content;
  final String excerpt;

  PageContentModel({
    required this.id,
    required this.title,
    required this.content,
    required this.excerpt,
  });

  factory PageContentModel.fromJson(Map<String, dynamic> json) {
    return PageContentModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      excerpt: json['excerpt'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'excerpt': excerpt,
    };
  }
}
