import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/shipment_search/models/delivery_search_response.model.dart';
import 'package:dropx/src/screens/shipment_search/models/search_request.model.dart';
import 'package:xr_helper/xr_helper.dart';

class ShipmentSearchRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ShipmentSearchRepository({
    required this.networkApiService,
  });

  // * Search Delivery
  Future<DeliverySearchResponse> searchDelivery({
    required SearchRequest searchRequest,
  }) async {
    return baseFunction(
      () async {
        final queryParams = searchRequest.toQueryParams();
        final url =
            '${ApiEndpoints.deliverySearch}?${_buildQueryString(queryParams)}';

        final response = await networkApiService.getResponse(url);

        return DeliverySearchResponse.fromJson(response);
      },
    );
  }

  // * Add Attendance
  Future<bool> addAttendance({
    required AttendanceRequest attendanceRequest,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.addAttendance;

        await networkApiService.postResponse(
          url,
          body: attendanceRequest.toJson(),
          showMessage: false,
        );

        return true;
      },
    );
  }

  // * Delete Attendance
  Future<bool> deleteAttendance() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.deleteAttendance;

        await networkApiService.postResponse(
          url,
          body: {},
          showMessage: false,
        );

        return true;
      },
    );
  }

  // * Add Interest
  Future<bool> addInterest({
    required InterestRequest interestRequest,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.deliveryInterest;

        await networkApiService.postResponse(url,
            body: interestRequest.toJson());

        return true;
      },
    );
  }

  // Helper method to build query string
  String _buildQueryString(Map<String, String> params) {
    return params.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');
  }
}
