// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/widgets.dart';

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/create_shipment.png
  AssetGenImage get createShipment =>
      const AssetGenImage('assets/icons/create_shipment.png');

  /// File path: assets/icons/package.png
  AssetGenImage get package => const AssetGenImage('assets/icons/package.png');

  /// File path: assets/icons/package_shipment.png
  AssetGenImage get packageShipment =>
      const AssetGenImage('assets/icons/package_shipment.png');

  /// File path: assets/icons/pending_package.png
  AssetGenImage get pendingPackage =>
      const AssetGenImage('assets/icons/pending_package.png');

  /// File path: assets/icons/search_shipment.png
  AssetGenImage get searchShipment =>
      const AssetGenImage('assets/icons/search_shipment.png');

  /// File path: assets/icons/waiting_for_person_package.png
  AssetGenImage get waitingForPersonPackage =>
      const AssetGenImage('assets/icons/waiting_for_person_package.png');

  /// File path: assets/icons/with_person_package.png
  AssetGenImage get withPersonPackage =>
      const AssetGenImage('assets/icons/with_person_package.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        createShipment,
        package,
        packageShipment,
        pendingPackage,
        searchShipment,
        waitingForPersonPackage,
        withPersonPackage
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/logo_symbol.png
  AssetGenImage get logoSymbol =>
      const AssetGenImage('assets/images/logo_symbol.png');

  /// File path: assets/images/onboarding1.webp
  AssetGenImage get onboarding1 =>
      const AssetGenImage('assets/images/onboarding1.webp');

  /// File path: assets/images/onboarding2.webp
  AssetGenImage get onboarding2 =>
      const AssetGenImage('assets/images/onboarding2.webp');

  /// File path: assets/images/onboarding3.webp
  AssetGenImage get onboarding3 =>
      const AssetGenImage('assets/images/onboarding3.webp');

  /// File path: assets/images/top_clipper.png
  AssetGenImage get topClipper =>
      const AssetGenImage('assets/images/top_clipper.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [logo, logoSymbol, onboarding1, onboarding2, onboarding3, topClipper];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}
