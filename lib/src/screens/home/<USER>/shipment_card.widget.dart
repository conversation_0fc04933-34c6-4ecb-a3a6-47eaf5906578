import 'dart:developer';

import 'package:dropx/src/core/consts/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';

import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:dropx/src/core/shared/widgets/dialogs/base_confirm_dialog.dart';
import 'package:dropx/src/core/shared/widgets/dialogs/order_assignment_success_dialog.dart';
import 'package:dropx/src/core/shared/services/location_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';

class ShipmentCardWidget extends HookConsumerWidget {
  final OrderModel order;
  final VoidCallback? onTap;
  final bool isExpanded;
  final bool showMoney;

  const ShipmentCardWidget({
    super.key,
    required this.order,
    this.onTap,
    this.isExpanded = false,
    this.showMoney = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 4.h,
          ),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: isExpanded
                  ? _getStatusColor(order.status)
                  : ColorManager.lightGrey,
              width: isExpanded ? 2 : 1,
            ),
            boxShadow: isExpanded
                ? []
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(isExpanded ? 0.1 : 0.05),
                      spreadRadius: 0,
                      blurRadius: isExpanded ? 12 : 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  // Package Icon
                  Assets.icons.package.image(),

                  AppGaps.gap16,

                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // From - To
                        Row(
                          children: [
                            Text(
                              context.tr.from,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: ColorManager.greyText,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                order.fromStation?.name ?? '',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),

                        AppGaps.gap4,

                        Row(
                          children: [
                            Text(
                              context.tr.to,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: ColorManager.greyText,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                order.toStation?.name ?? '',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),

                        AppGaps.gap8,

                        if (order.price.isNotEmpty && showMoney) ...[
                          // Price
                          Row(
                            children: [
                              Text(
                                context.tr.price,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: ColorManager.greyText,
                                ),
                              ),
                              Text(
                                '${order.price} ${context.tr.currency}',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                            ],
                          ),

                          AppGaps.gap8,
                        ],

                        // show points
                        if (order.points.isNotEmpty && !showMoney) ...[
                          Row(
                            children: [
                              Text(
                                '${context.tr.points}: ',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: ColorManager.greyText,
                                ),
                              ),
                              Text(
                                order.points,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                            ],
                          ),
                          AppGaps.gap8,
                        ],

                        // Status
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(order.status)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: _getStatusColor(order.status),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                order.statusText,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: _getStatusColor(order.status),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Assets.icons.pendingPackage.image(
                              width: 40,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Expanded content
              if (isExpanded) ...[
                AppGaps.gap16,
                _buildExpandedContent(context, ref),
              ],
            ],
          ),
        ));
  }

  Widget _buildExpandedContent(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ColorManager.lightGrey.withOpacity(0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Details Header
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: _getStatusColor(order.status),
                size: 20,
              ),
              AppGaps.gap8,
              Text(
                context.tr.orderDetails,
                style: AppTextStyles.labelLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getStatusColor(order.status),
                ),
              ),
            ],
          ),
          AppGaps.gap8,

          // Receiver Information
          if (order.receiverName.isNotEmpty) ...[
            _buildDetailRow(
              context,
              Icons.person,
              context.tr.receiverName,
              order.receiverName,
            ),
            AppGaps.gap8,
          ],

          if (order.receiverPhone.isNotEmpty) ...[
            _buildDetailRow(
              context,
              Icons.phone,
              context.tr.receiverPhone,
              order.receiverPhone,
            ),
            AppGaps.gap8,
          ],

          if (order.type.isNotEmpty) ...[
            _buildDetailRow(
              context,
              Icons.category,
              context.tr.orderType,
              order.type,
            ),
            AppGaps.gap8,
          ],

          if (order.note.isNotEmpty && order.note != 'null') ...[
            _buildDetailRow(
              context,
              Icons.note,
              context.tr.notes,
              order.note,
            ),
            AppGaps.gap16,
          ],

          if (order.status == 'waiting_for_order') ...[
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 20,
                ),
                AppGaps.gap8,
                Text(
                  '${context.tr.remainingTimeToCancelTheOrder}: ${order.remainingMinutes ?? 0}',
                  style: AppTextStyles.labelLarge.copyWith(),
                ),
              ],
            ),
            AppGaps.gap12,
          ],
          // Status Change Button
          _buildStatusChangeButton(context, ref),

          // make cancel if order.status == 'waiting_for_order'
          if (order.status == 'waiting_for_order' ||
              order.status == 'pending') ...[
            AppGaps.gap16,
            _buildCancelOrderButton(context, ref),
          ],
        ],
      ),
    );
  }

  //_buildCancelOrderButton
  Widget _buildCancelOrderButton(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: 50,
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () => {},
        style: OutlinedButton.styleFrom(
          foregroundColor: ColorManager.errorColor,
          side: const BorderSide(
            color: ColorManager.errorColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          context.tr.cancelOrder,
          style: AppTextStyles.labelMedium.copyWith(
            color: ColorManager.errorColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
      BuildContext context, IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: ColorManager.greyIcon,
        ),
        AppGaps.gap8,
        Expanded(
          child: RichText(
            text: TextSpan(
              style: AppTextStyles.bodyMedium,
              children: [
                TextSpan(
                  text: '$label: ',
                  style: AppTextStyles.bodyMedium.copyWith(
                      color: ColorManager.greyText,
                      fontFamily: AppConsts.fontFamily),
                ),
                TextSpan(
                  text: value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontFamily: AppConsts.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChangeButton(BuildContext context, WidgetRef ref) {
    String buttonText = '';
    String nextStatus = '';
    Color buttonColor = _getStatusColor(order.status);

    switch (order.status.toLowerCase()) {
      case 'pending':
        buttonText = context.tr.deliverToDepartureStationButton;
        nextStatus = 'confirmed';
        break;
      case 'waiting_for_order':
        buttonText = context.tr.pickupFromDepartureStationButton;
        nextStatus = 'picked_up';
        break;
      case 'picked_up':
        buttonText = context.tr.deliverToDestinationButton;
        nextStatus = 'delivered';
        break;
      default:
        return const SizedBox
            .shrink(); // No button for completed/cancelled orders
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _handleStatusChange(context, nextStatus, ref),
        icon: Icon(
          Icons.location_on,
          size: 18,
          color: Colors.white,
        ),
        label: Text(
          buttonText,
          style: AppTextStyles.labelMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'waiting_for_order':
        return Colors.orange;
      case 'picked_up':
        return Colors.indigo;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return ColorManager.greyText;
    }
  }

  Future<void> _handleStatusChange(
      BuildContext context, String newStatus, WidgetRef ref) async {
    try {
      // Get current location first
      final currentPosition = await LocationService.getCurrentLocation();
      if (currentPosition == null) {
        if (context.mounted) {
          showToast(context.tr.unableToGetCurrentLocation, isError: true);
        }
        return;
      }

      // Determine which station to check based on status
      Station? targetStation;
      String confirmMessage = '';
      String buttonText = '';

      switch (newStatus) {
        case 'confirmed':
          // Check distance to from station
          targetStation = order.fromStation;
          confirmMessage =
              context.tr.areYouSureConfirmDeliveryToDepartureStation;
          buttonText = context.tr.deliverToDepartureStation;
          break;
        case 'picked_up':
          // Check distance to from station
          targetStation = order.fromStation;
          confirmMessage =
              context.tr.areYouSureConfirmPickupFromDepartureStation;
          buttonText = context.tr.pickupFromDepartureStation;
          break;
        case 'delivered':
          // Check distance to to station
          targetStation = order.toStation;
          confirmMessage = context.tr.areYouSureConfirmDeliveryToDestination;
          buttonText = context.tr.deliverToDestination;
          break;
      }

      log('ffff ${targetStation?.name} FFFFS ${targetStation?.latitude} Ff ${targetStation?.longitude}');

      // Check distance if station coordinates are available
      if (targetStation != null) {
        final stationLat = double.tryParse(targetStation.latitude);
        final stationLng = double.tryParse(targetStation.longitude);

        if (stationLat != null && stationLng != null) {
          // Calculate distance
          final distanceInMeters = Geolocator.distanceBetween(
            currentPosition.latitude,
            currentPosition.longitude,
            stationLat,
            stationLng,
          );

          // Check if distance is more than 100 meters
          // if (distanceInMeters > 100) {
          //   if (context.mounted) {
          //     showToast(
          //       context.tr.cannotPerformActionTooFarFromStation(
          //         buttonText.toLowerCase(),
          //         '${distanceInMeters.round()}',
          //       ),
          //       isError: true,
          //     );
          //   }
          //   return;
          // }
        }
      }

      // Show confirmation dialog
      if (!context.mounted) return;
      final confirmed = await BaseConfirmDialog.show(
        context: context,
        title: context.tr.confirmStatusChange,
        message: confirmMessage,
        confirmText: buttonText,
        icon: Icons.location_on,
      );

      if (confirmed == true) {
        // Distance validation already performed in _handleStatusChange method

        if (newStatus == 'picked_up') {
          // Use delivery/get-order API for pickup status with interest ID
          final updatedOrder =
              await ref.read(ordersControllerProvider).getOrderByInterestId(
                    interestId: order.id ?? 0,
                  );

          // Show success dialog with barcode
          if (context.mounted) {
            await OrderAssignmentSuccessDialog.show(
              context: context,
              barcode: updatedOrder.barcode ?? '',
              orderType: updatedOrder.type,
            );
          }
        } else {
          // Use regular status change API for other statuses
          await ref.read(ordersControllerProvider).changeOrderStatus(
                orderId: order.id,
                status: newStatus,
              );
        }

        // Refresh orders list
        ref.invalidate(getOrdersFutureProvider);
        ref.invalidate(getOrdersHistoryFutureProvider(order.status));

        // Refresh orders history reactive providers for both sent and received tabs
        ref.invalidate(getSentOrdersHistoryProvider);
        ref.invalidate(getReceivedOrdersHistoryProvider);
      }
    } catch (e) {
      Log.e('Error changing status: $e');
      if (context.mounted) {
        showToast(context.tr.errorUpdatingStatus, isError: true);
      }
    }
  }
}
