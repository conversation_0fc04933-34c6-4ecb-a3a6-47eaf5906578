import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class EmptyDataWidget extends StatelessWidget {
  const EmptyDataWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(
            Icons.sentiment_dissatisfied,
            size: 100,
            color: ColorManager.grey,
          ),
          AppGaps.gap12,
          Text(
            context.tr.noDataFound,
            style: AppTextStyles.title,
          ),
        ],
      ),
    );
  }
}
