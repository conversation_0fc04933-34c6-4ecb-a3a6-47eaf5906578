import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:dropx/src/core/shared/services/location_service.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import '../fields/base_search_sheet.dart';
import '../loading/loading_widget.dart';

final stations = ValueNotifier<List<Station>>([]);

class StationDropdown extends HookConsumerWidget {
  final String label;
  final ValueNotifier<Station?> selectedStation;
  final Widget? icon;

  const StationDropdown({
    super.key,
    required this.label,
    required this.selectedStation,
    this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    useEffect(() {
      // Get current location when widget mounts
      if (LocationService.currentLocation.value == null) {
        LocationService.getCurrentLocation().then((value) {
          ordersController
              .getStationsWithLocation(
            lat: value?.latitude,
            lng: value?.longitude,
          )
              .then((value) {
            stations.value = value;
          });
        });
      } else {
        ordersController.getStationsWithLocation().then((value) {
          stations.value = value;
        });
      }
      return () {};
    }, []);

    if (ordersController.isLoading) return const LoadingWidget();

    return ValueListenableBuilder(
        valueListenable: stations,
        builder: (context, stations, child) {
          return BaseSearchSheet(
            label: label,
            selectedValue: selectedStation.value,
            data: stations,
            icon: icon,
            itemModelAsName: (station) => (station as Station).name,
            onChanged: (selectedStation) {
              this.selectedStation.value = selectedStation as Station?;
            },
          );
        });
  }
}
