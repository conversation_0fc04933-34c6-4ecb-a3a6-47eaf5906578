class DeliverySearchResponse {
  final bool success;
  final String message;
  final List<DeliverySearchItem> data;

  DeliverySearchResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory DeliverySearchResponse.fromJson(Map<String, dynamic> json) {
    return DeliverySearchResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => DeliverySearchItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

class DeliverySearchItem {
  final String typeName;
  final int shipmentsCount;
  final int interestedPeople;
  final String deliveryChance;

  DeliverySearchItem({
    required this.typeName,
    required this.shipmentsCount,
    required this.interestedPeople,
    required this.deliveryChance,
  });

  factory DeliverySearchItem.fromJson(Map<String, dynamic> json) {
    return DeliverySearchItem(
      typeName: json['type_name'] as String,
      shipmentsCount: json['shipments_count'] as int,
      interestedPeople: json['interested_people'] as int,
      deliveryChance: json['delivery_chance'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type_name': typeName,
      'shipments_count': shipmentsCount,
      'interested_people': interestedPeople,
      'delivery_chance': deliveryChance,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeliverySearchItem && other.typeName == typeName;
  }

  @override
  int get hashCode => typeName.hashCode;

  @override
  String toString() => typeName;
}
